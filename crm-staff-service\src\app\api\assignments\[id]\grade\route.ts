// Assignment Grading API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { z } from "zod";

const gradeSubmissionSchema = z.object({
  studentId: z.string().min(1, "Student ID is required"),
  grade: z.string().min(1, "Grade is required"),
  points: z.number().min(0).optional(),
  feedback: z.string().optional(),
});

// POST /api/assignments/[id]/grade - Grade a student's submission
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to grade
    if (!["teacher", "manager"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = gradeSubmissionSchema.parse(body);

    // Verify assignment exists and user has access
    const assignment = await prisma.assignment.findUnique({
      where: { id: params.id },
      include: {
        group: {
          include: {
            teacher: {
              include: {
                staffUser: true,
              },
            },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json({ error: "Assignment not found" }, { status: 404 });
    }

    // If user is a teacher, verify they own this assignment's group
    if (session.user.role === "teacher" && assignment.group.teacher.staffUser.id !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // TODO: Call student service to update the grade
    // This would involve making an API call to the student service
    // to update the submission with the grade and feedback

    const gradeData = {
      assignmentId: params.id,
      studentId: validatedData.studentId,
      grade: validatedData.grade,
      points: validatedData.points,
      feedback: validatedData.feedback,
      gradedBy: session.user.id,
      gradedAt: new Date(),
    };

    // Placeholder response - in real implementation, this would call student service
    return NextResponse.json({
      success: true,
      data: gradeData,
      message: "Grade recorded successfully. This would integrate with student service to update the actual submission.",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error grading submission:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
