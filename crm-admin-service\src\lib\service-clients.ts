// Service-to-service communication clients
// This file handles communication between the admin service and other services

import { ServiceAuthManager, ServiceClient, ServiceRetryManager } from '../../../crm-shared-types/src/lib/service-auth';

// Initialize service authentication
const authManager = new ServiceAuthManager({
  serviceId: 'admin-service',
  serviceName: 'admin',
  secretKey: process.env.SERVICE_SECRET_KEY || 'admin-service-secret-key',
  allowedServices: ['staff', 'student'],
});

const serviceClient = new ServiceClient(authManager);

// Staff Service Communication
export async function notifyStaffService(data: any) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.post('staff', '/api/internal/notifications', data, ['write']);
    });
  } catch (error) {
    console.error('Failed to notify staff service:', error);
    return { success: false, error: error.message };
  }
}

export async function getStaffUserInfo(staffUserId: string) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.get('staff', `/api/internal/users/${staffUserId}`, ['read']);
    });
  } catch (error) {
    console.error('Failed to get staff user info:', error);
    return null;
  }
}

export async function createStaffUser(userData: any) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.post('staff', '/api/internal/users', userData, ['write']);
    });
  } catch (error) {
    console.error('Failed to create staff user:', error);
    return { success: false, error: error.message };
  }
}

// Student Service Communication
export async function notifyStudentService(data: any) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.post('student', '/api/internal/notifications', data, ['write']);
    });
  } catch (error) {
    console.error('Failed to notify student service:', error);
    return { success: false, error: error.message };
  }
}

export async function getStudentInfo(studentId: string) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.get('student', `/api/internal/students/${studentId}`, ['read']);
    });
  } catch (error) {
    console.error('Failed to get student info:', error);
    return null;
  }
}

export async function createStudentUser(userData: any) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.post('student', '/api/internal/users', userData, ['write']);
    });
  } catch (error) {
    console.error('Failed to create student user:', error);
    return { success: false, error: error.message };
  }
}

// Payment verification with student service
export async function verifyStudentPayment(paymentData: any) {
  try {
    return await ServiceRetryManager.withRetry(async () => {
      return await serviceClient.post('student', '/api/internal/payments/verify', paymentData, ['write']);
    });
  } catch (error) {
    console.error('Failed to verify student payment:', error);
    return { success: false, error: error.message };
  }
}

// Export auth manager for internal API endpoints
export { authManager };
