// Assignment Submission API - Student Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { SubmissionStatus } from "@prisma/client";
import { z } from "zod";

const submitAssignmentSchema = z.object({
  submissionContent: z.string().optional(),
  fileUrl: z.string().url().optional(),
});

// POST /api/assignments/[id]/submit - Submit assignment
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = submitAssignmentSchema.parse(body);

    // Get student record
    const student = await prisma.student.findFirst({
      where: { studentUserId: session.user.id },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    // Check if submission already exists
    const existingSubmission = await prisma.assignmentSubmission.findFirst({
      where: {
        studentId: student.id,
        assignmentId: params.id,
      },
    });

    let submission;

    if (existingSubmission) {
      // Update existing submission
      submission = await prisma.assignmentSubmission.update({
        where: { id: existingSubmission.id },
        data: {
          submissionContent: validatedData.submissionContent,
          fileUrl: validatedData.fileUrl,
          submittedAt: new Date(),
          status: SubmissionStatus.submitted,
        },
      });
    } else {
      // Create new submission
      submission = await prisma.assignmentSubmission.create({
        data: {
          studentId: student.id,
          assignmentId: params.id,
          submissionContent: validatedData.submissionContent,
          fileUrl: validatedData.fileUrl,
          status: SubmissionStatus.submitted,
        },
      });
    }

    // Update progress
    await prisma.studentProgress.upsert({
      where: {
        studentId_assignmentId: {
          studentId: student.id,
          assignmentId: params.id,
        },
      },
      update: {
        progressPercentage: 100,
        submittedAt: new Date(),
      },
      create: {
        studentId: student.id,
        assignmentId: params.id,
        progressPercentage: 100,
        submittedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      data: submission,
      message: "Assignment submitted successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error submitting assignment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
