/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/@auth/core/errors.js":
/*!*******************************************!*\
  !*** ./node_modules/@auth/core/errors.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDenied: () => (/* binding */ AccessDenied),\n/* harmony export */   AccountNotLinked: () => (/* binding */ AccountNotLinked),\n/* harmony export */   AdapterError: () => (/* binding */ AdapterError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   CallbackRouteError: () => (/* binding */ CallbackRouteError),\n/* harmony export */   CredentialsSignin: () => (/* binding */ CredentialsSignin),\n/* harmony export */   DuplicateConditionalUI: () => (/* binding */ DuplicateConditionalUI),\n/* harmony export */   EmailSignInError: () => (/* binding */ EmailSignInError),\n/* harmony export */   ErrorPageLoop: () => (/* binding */ ErrorPageLoop),\n/* harmony export */   EventError: () => (/* binding */ EventError),\n/* harmony export */   ExperimentalFeatureNotEnabled: () => (/* binding */ ExperimentalFeatureNotEnabled),\n/* harmony export */   InvalidCallbackUrl: () => (/* binding */ InvalidCallbackUrl),\n/* harmony export */   InvalidCheck: () => (/* binding */ InvalidCheck),\n/* harmony export */   InvalidEndpoints: () => (/* binding */ InvalidEndpoints),\n/* harmony export */   InvalidProvider: () => (/* binding */ InvalidProvider),\n/* harmony export */   JWTSessionError: () => (/* binding */ JWTSessionError),\n/* harmony export */   MissingAdapter: () => (/* binding */ MissingAdapter),\n/* harmony export */   MissingAdapterMethods: () => (/* binding */ MissingAdapterMethods),\n/* harmony export */   MissingAuthorize: () => (/* binding */ MissingAuthorize),\n/* harmony export */   MissingCSRF: () => (/* binding */ MissingCSRF),\n/* harmony export */   MissingSecret: () => (/* binding */ MissingSecret),\n/* harmony export */   MissingWebAuthnAutocomplete: () => (/* binding */ MissingWebAuthnAutocomplete),\n/* harmony export */   OAuthAccountNotLinked: () => (/* binding */ OAuthAccountNotLinked),\n/* harmony export */   OAuthCallbackError: () => (/* binding */ OAuthCallbackError),\n/* harmony export */   OAuthProfileParseError: () => (/* binding */ OAuthProfileParseError),\n/* harmony export */   OAuthSignInError: () => (/* binding */ OAuthSignInError),\n/* harmony export */   SessionTokenError: () => (/* binding */ SessionTokenError),\n/* harmony export */   SignInError: () => (/* binding */ SignInError),\n/* harmony export */   SignOutError: () => (/* binding */ SignOutError),\n/* harmony export */   UnknownAction: () => (/* binding */ UnknownAction),\n/* harmony export */   UnsupportedStrategy: () => (/* binding */ UnsupportedStrategy),\n/* harmony export */   UntrustedHost: () => (/* binding */ UntrustedHost),\n/* harmony export */   Verification: () => (/* binding */ Verification),\n/* harmony export */   WebAuthnVerificationError: () => (/* binding */ WebAuthnVerificationError),\n/* harmony export */   isClientError: () => (/* binding */ isClientError)\n/* harmony export */ });\n/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nclass AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nclass SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nclass AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nclass AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nclass CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nclass ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nclass EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nclass InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nclass CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nclass InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nclass InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nclass JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nclass MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nclass MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nclass MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nclass MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nclass OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nclass OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nclass OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nclass SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nclass OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nclass EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nclass SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nclass UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nclass UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nclass InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nclass UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nclass Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nclass MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nfunction isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nclass DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nclass MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nclass WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nclass AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nclass ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@auth/core/errors.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(app-pages-browser)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ var _s = $RefreshSig$();\n\n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger) {\n    let req = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : {};\n    const url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n    try {\n        var _req_headers;\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(req === null || req === void 0 ? void 0 : (_req_headers = req.headers) === null || _req_headers === void 0 ? void 0 : _req_headers.cookie) ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req === null || req === void 0 ? void 0 : req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (false) {}\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    _s();\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n_s(useOnline, \"9TTTpdMr0LAEvNmxVj+grReKWwQ=\");\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = \"https://\".concat(url);\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = \"\".concat(_url.origin).concat(path);\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-auth/lib/client.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("var react__WEBPACK_IMPORTED_MODULE_1___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(app-pages-browser)/./node_modules/next-auth/lib/client.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ var _React_createContext;\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nvar _process_env_NEXTAUTH_URL, _process_env_NEXTAUTH_URL_INTERNAL, _ref, _process_env_NEXTAUTH_URL_INTERNAL1;\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_process_env_NEXTAUTH_URL = process.env.NEXTAUTH_URL) !== null && _process_env_NEXTAUTH_URL !== void 0 ? _process_env_NEXTAUTH_URL : process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_ref = (_process_env_NEXTAUTH_URL_INTERNAL = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process_env_NEXTAUTH_URL_INTERNAL !== void 0 ? _process_env_NEXTAUTH_URL_INTERNAL : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)((_process_env_NEXTAUTH_URL_INTERNAL1 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process_env_NEXTAUTH_URL_INTERNAL1 !== void 0 ? _process_env_NEXTAUTH_URL_INTERNAL1 : process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = (_React_createContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext) === null || _React_createContext === void 0 ? void 0 : _React_createContext.call(/*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_1___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_1__, 2))), undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    _s();\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options !== null && options !== void 0 ? options : {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = \"\".concat(__NEXTAUTH.basePath, \"/signin?\").concat(new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                }));\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\n_s(useSession, \"fo5J65/TYWua//m8QE+oTrUC6Kk=\");\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    var _params_broadcast;\n    if ((_params_broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params_broadcast !== void 0 ? _params_broadcast : true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    var _response_csrfToken;\n    return (_response_csrfToken = response === null || response === void 0 ? void 0 : response.csrfToken) !== null && _response_csrfToken !== void 0 ? _response_csrfToken : \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options !== null && options !== void 0 ? options : {};\n    const { redirect = true, redirectTo = callbackUrl !== null && callbackUrl !== void 0 ? callbackUrl : window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = \"\".concat(baseUrl, \"/error\");\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: redirectTo\n        }));\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            'Provider id \"'.concat(provider, '\" refers to a WebAuthn provider.'),\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = \"\".concat(baseUrl, \"/\").concat(providerType === \"credentials\" ? \"callback\" : \"signin\", \"/\").concat(provider);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(\"\".concat(signInUrl, \"?\").concat(new URLSearchParams(authorizationParams)), {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        var _data_url;\n        const url = (_data_url = data.url) !== null && _data_url !== void 0 ? _data_url : redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    var _searchParams_get;\n    const error = (_searchParams_get = new URL(data.url).searchParams.get(\"error\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : undefined;\n    var _searchParams_get1;\n    const code = (_searchParams_get1 = new URL(data.url).searchParams.get(\"code\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    var _options_callbackUrl;\n    const { redirect = true, redirectTo = (_options_callbackUrl = options === null || options === void 0 ? void 0 : options.callbackUrl) !== null && _options_callbackUrl !== void 0 ? _options_callbackUrl : window.location.href } = options !== null && options !== void 0 ? options : {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(\"\".concat(baseUrl, \"/signout\"), {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        var _data_url;\n        const url = (_data_url = data.url) !== null && _data_url !== void 0 ? _data_url : redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    _s1();\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async function() {\n                    let { event } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n_s1(SessionProvider, \"2gNWkA1ll3+1JambdW4uXB3blzw=\", false, function() {\n    return [\n        _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline\n    ];\n});\n_c = SessionProvider;\nvar _c;\n$RefreshReg$(_c, \"SessionProvider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-auth/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-auth/react.js */ \"(app-pages-browser)/./node_modules/next-auth/react.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/globals.css */ \"(app-pages-browser)/./src/styles/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDV2luZG93cyUyMDExJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0lubm92YXRpdmUlMjBDZW50cmUlNUMlNUNjcm0tYWRtaW4tc2VydmljZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtYXV0aCU1QyU1Q3JlYWN0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2Vzc2lvblByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1dpbmRvd3MlMjAxMSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNJbm5vdmF0aXZlJTIwQ2VudHJlJTVDJTVDY3JtLWFkbWluLXNlcnZpY2UlNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFxTTtBQUNyTTtBQUNBLDBLQUF1SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2Vzc2lvblByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcV2luZG93cyAxMVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxJbm5vdmF0aXZlIENlbnRyZVxcXFxjcm0tYWRtaW4tc2VydmljZVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1hdXRoXFxcXHJlYWN0LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxXaW5kb3dzIDExXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXElubm92YXRpdmUgQ2VudHJlXFxcXGNybS1hZG1pbi1zZXJ2aWNlXFxcXHNyY1xcXFxzdHlsZXNcXFxcZ2xvYmFscy5jc3NcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"674aa9fd1fa1\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcSW5ub3ZhdGl2ZSBDZW50cmVcXGNybS1hZG1pbi1zZXJ2aWNlXFxzcmNcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2NzRhYTlmZDFmYTFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-admin-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);