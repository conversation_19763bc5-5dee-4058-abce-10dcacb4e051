// Assignment Submissions Tracking API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

// GET /api/assignments/[id]/submissions - Get submissions for an assignment
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to view submissions
    if (!["teacher", "manager"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Verify assignment exists and user has access
    const assignment = await prisma.assignment.findUnique({
      where: { id: params.id },
      include: {
        group: {
          include: {
            teacher: {
              include: {
                staffUser: true,
              },
            },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json({ error: "Assignment not found" }, { status: 404 });
    }

    // If user is a teacher, verify they own this assignment's group
    if (session.user.role === "teacher" && assignment.group.teacher.staffUser.id !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const status = searchParams.get("status");

    const skip = (page - 1) * limit;

    // This would typically call the student service to get submissions
    // For now, we'll return a placeholder structure
    const submissionsData = {
      assignmentId: params.id,
      assignmentTitle: assignment.title,
      dueDate: assignment.dueDate,
      totalStudents: assignment.group.currentStudents,
      submissions: [], // Would be populated from student service
      submissionStats: {
        submitted: 0,
        graded: 0,
        pending: 0,
        late: 0,
      },
    };

    // TODO: Call student service to get actual submission data
    // const studentServiceResponse = await fetch(`${process.env.STUDENT_SERVICE_URL}/api/submissions?assignmentId=${params.id}`);

    return NextResponse.json({
      success: true,
      data: submissionsData,
      message: "This endpoint would integrate with student service to fetch actual submissions",
    });
  } catch (error) {
    console.error("Error fetching submissions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
