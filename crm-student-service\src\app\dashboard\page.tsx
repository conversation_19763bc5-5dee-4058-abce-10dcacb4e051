"use client";

import { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { 
  BookOpen, 
  Calendar, 
  TrendingUp, 
  FileText, 
  Clock,
  LogOut,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface StudentProgress {
  student: {
    id: string;
    studentId: string;
    name: string;
    email: string;
    currentLevel: string;
    status: string;
    enrollmentDate: string;
  };
  statistics: {
    totalAssignments: number;
    completedAssignments: number;
    gradedAssignments: number;
    averageProgress: number;
    completionRate: number;
  };
  gradeDistribution: Record<string, number>;
  progressRecords: any[];
}

export default function StudentDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [progress, setProgress] = useState<StudentProgress | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (session) {
      fetchProgress();
    }
  }, [session]);

  const fetchProgress = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/progress");
      const data = await response.json();
      
      if (data.success) {
        setProgress(data.data);
      }
    } catch (error) {
      console.error("Error fetching progress:", error);
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Please sign in to access the student portal.</p>
        </div>
      </div>
    );
  }

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Student Portal</h1>
          <p className="text-gray-600">Track your progress and access learning resources</p>
          {progress && (
            <p className="text-sm text-gray-500 mt-1">
              Welcome back, {progress.student.name} ({progress.student.studentId})
              {progress.student.currentLevel && ` - Level: ${progress.student.currentLevel}`}
            </p>
          )}
        </div>
        <Button onClick={handleSignOut} variant="outline" className="flex items-center gap-2">
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>
      </div>

      {progress && (
        <>
          {/* Progress Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <BookOpen className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Assignments</p>
                    <p className="text-2xl font-bold text-gray-900">{progress.statistics.totalAssignments}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-gray-900">{progress.statistics.completedAssignments}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Average Progress</p>
                    <p className="text-2xl font-bold text-gray-900">{progress.statistics.averageProgress}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Graded</p>
                    <p className="text-2xl font-bold text-gray-900">{progress.statistics.gradedAssignments}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Overall Progress */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Overall Progress</CardTitle>
              <CardDescription>Your completion rate across all assignments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>Completion Rate</span>
                    <span>{progress.statistics.completionRate}%</span>
                  </div>
                  <Progress value={progress.statistics.completionRate} className="h-2" />
                </div>
                <div>
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span>Average Progress</span>
                    <span>{progress.statistics.averageProgress}%</span>
                  </div>
                  <Progress value={progress.statistics.averageProgress} className="h-2" />
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Main Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Assignments */}
        <Card>
          <CardHeader>
            <CardTitle>My Assignments</CardTitle>
            <CardDescription>View and submit your assignments</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/assignments")}
              >
                <div className="text-left">
                  <div className="font-medium">View All Assignments</div>
                  <div className="text-sm text-gray-600">Check due dates and submit work</div>
                </div>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/assignments?status=pending")}
              >
                <AlertCircle className="h-4 w-4 mr-2 text-orange-500" />
                <div className="text-left">
                  <div className="font-medium">Pending Assignments</div>
                  <div className="text-sm text-gray-600">Work that needs to be completed</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Schedule */}
        <Card>
          <CardHeader>
            <CardTitle>My Schedule</CardTitle>
            <CardDescription>View your class timetable</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/schedule")}
              >
                <Calendar className="h-4 w-4 mr-2" />
                <div className="text-left">
                  <div className="font-medium">View Schedule</div>
                  <div className="text-sm text-gray-600">Check your class times and locations</div>
                </div>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <Clock className="h-4 w-4 mr-2" />
                <div className="text-left">
                  <div className="font-medium">Today's Classes</div>
                  <div className="text-sm text-gray-600">See what's happening today</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Progress Tracking */}
        <Card>
          <CardHeader>
            <CardTitle>Progress Tracking</CardTitle>
            <CardDescription>Monitor your learning progress</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/progress")}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                <div className="text-left">
                  <div className="font-medium">Detailed Progress</div>
                  <div className="text-sm text-gray-600">View comprehensive progress reports</div>
                </div>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <FileText className="h-4 w-4 mr-2" />
                <div className="text-left">
                  <div className="font-medium">Grade History</div>
                  <div className="text-sm text-gray-600">Review your grades and feedback</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Resources */}
        <Card>
          <CardHeader>
            <CardTitle>Learning Resources</CardTitle>
            <CardDescription>Access course materials and resources</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/resources")}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                <div className="text-left">
                  <div className="font-medium">Course Materials</div>
                  <div className="text-sm text-gray-600">Access textbooks, videos, and documents</div>
                </div>
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
              >
                <FileText className="h-4 w-4 mr-2" />
                <div className="text-left">
                  <div className="font-medium">Study Guides</div>
                  <div className="text-sm text-gray-600">Download study materials and guides</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
