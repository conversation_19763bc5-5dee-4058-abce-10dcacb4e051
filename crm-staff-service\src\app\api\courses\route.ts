// Courses Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { CourseStatus } from "@prisma/client";
import { z } from "zod";

const createCourseSchema = z.object({
  courseCode: z.string().min(1, "Course code is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  level: z.string().min(1, "Level is required"),
  durationWeeks: z.number().positive("Duration must be positive"),
  maxStudents: z.number().positive("Max students must be positive"),
  price: z.number().positive("Price must be positive"),
});

const updateCourseSchema = z.object({
  courseCode: z.string().min(1).optional(),
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  level: z.string().min(1).optional(),
  durationWeeks: z.number().positive().optional(),
  maxStudents: z.number().positive().optional(),
  price: z.number().positive().optional(),
  status: z.nativeEnum(CourseStatus).optional(),
});

// GET /api/courses - List courses
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") as CourseStatus | null;
    const level = searchParams.get("level");

    const skip = (page - 1) * limit;

    const where: any = {};
    if (status) where.status = status;
    if (level) where.level = level;

    const [courses, total] = await Promise.all([
      prisma.course.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          groups: {
            select: {
              id: true,
              groupName: true,
              currentStudents: true,
              maxStudents: true,
              status: true,
            },
          },
          _count: {
            select: {
              enrollments: true,
              groups: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.course.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        courses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching courses:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/courses - Create new course
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create courses
    if (!["manager", "teacher"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createCourseSchema.parse(body);

    // Check if course code already exists
    const existingCourse = await prisma.course.findUnique({
      where: { courseCode: validatedData.courseCode },
    });

    if (existingCourse) {
      return NextResponse.json(
        { error: "Course code already exists" },
        { status: 400 }
      );
    }

    const course = await prisma.course.create({
      data: {
        ...validatedData,
        createdBy: session.user.id,
      },
      include: {
        createdByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: course,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating course:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
