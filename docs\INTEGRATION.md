# CRM System Integration Guide

This document describes the inter-service communication and integration features of the Innovative Centre CRM system.

## 🏗️ Architecture Overview

The CRM system consists of three independent services that communicate securely:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Service │    │  Staff Service  │    │ Student Service │
│   Port: 3001    │    │   Port: 3002    │    │   Port: 3003    │
│                 │    │                 │    │                 │
│ • Payments      │◄──►│ • Leads         │◄──►│ • Assignments   │
│ • User Mgmt     │    │ • Courses       │    │ • Progress      │
│ • Audit Logs    │    │ • Teachers      │    │ • Resources     │
│ • Security      │    │ • Groups        │    │ • Schedule      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Shared Types    │
                    │ Authentication  │
                    │ Service Clients │
                    └─────────────────┘
```

## 🔐 Service Authentication

### Authentication Mechanism

All inter-service communication uses a secure signature-based authentication system:

1. **Service Identity**: Each service has a unique name and API key
2. **Request Signing**: Requests include timestamp and cryptographic signature
3. **Signature Verification**: Receiving service validates the signature
4. **Time-based Security**: Requests expire after 5 minutes

### Authentication Headers

```javascript
{
  'X-Service-Name': 'crm-staff-service',
  'X-Service-Timestamp': '1640995200000',
  'X-Service-Signature': 'base64-encoded-signature',
  'Content-Type': 'application/json'
}
```

### Implementation Example

```javascript
import { ServiceClientFactory } from 'crm-shared-types';

const serviceFactory = new ServiceClientFactory({
  adminServiceUrl: 'http://localhost:3001',
  staffServiceUrl: 'http://localhost:3002',
  studentServiceUrl: 'http://localhost:3003',
  serviceName: 'crm-staff-service',
  apiKey: process.env.SERVICE_API_KEY,
});

const adminClient = serviceFactory.getAdminClient();
const studentClient = serviceFactory.getStudentClient();
```

## 🔄 Data Synchronization

### Lead to Student Conversion

**Workflow**: Staff Service → Student Service → Admin Service

1. **Lead Creation** (Staff Service)
   - Staff creates lead with contact information
   - Lead stored in staff database

2. **Lead Conversion** (Staff Service → Student Service)
   - Staff converts qualified lead to student
   - Student record created in student service
   - Student user account created

3. **User Management** (Student Service → Admin Service)
   - User management record created in admin service
   - Cross-service user tracking enabled

```javascript
// Staff Service - Convert Lead
const student = await convertLeadToStudent({
  email: lead.email,
  firstName: lead.firstName,
  lastName: lead.lastName,
  dateOfBirth: '1990-01-01',
  currentLevel: 'beginner',
});

// Creates student in student service
// Creates user management record in admin service
```

### Schedule Synchronization

**Workflow**: Staff Service → Student Service

1. **Group Creation** (Staff Service)
   - Staff creates course groups with schedules
   - Group information stored in staff database

2. **Schedule Sync** (Student Service ← Staff Service)
   - Student service fetches group information
   - Student schedules updated with current groups
   - Schedule cached for offline access

```javascript
// Student Service - Sync Schedule
const syncResult = await studentClient.syncSchedule(studentId);
// Fetches groups from staff service
// Updates student schedule cache
```

### Assignment Distribution

**Workflow**: Staff Service → Student Service

1. **Assignment Creation** (Staff Service)
   - Teachers create assignments for groups
   - Assignment details stored in staff database

2. **Assignment Access** (Student Service ← Staff Service)
   - Students view assignments through student portal
   - Assignment data fetched from staff service
   - Progress tracked in student service

## 📊 Cross-Service Workflows

### Complete Student Enrollment

```mermaid
sequenceDiagram
    participant S as Staff Service
    participant St as Student Service
    participant A as Admin Service
    
    S->>S: Create Lead
    S->>S: Qualify Lead
    S->>St: Convert to Student
    St->>St: Create Student Record
    St->>A: Create User Management
    A->>A: Log User Creation
    S->>S: Update Lead Status
```

### Payment Processing

```mermaid
sequenceDiagram
    participant S as Staff Service
    participant A as Admin Service
    participant St as Student Service
    
    S->>A: Record Payment
    A->>A: Verify Payment
    A->>A: Create Audit Log
    A->>St: Update Student Status
    St->>St: Update Access Rights
```

### Assignment Workflow

```mermaid
sequenceDiagram
    participant T as Teacher (Staff)
    participant S as Staff Service
    participant St as Student Service
    participant Stu as Student
    
    T->>S: Create Assignment
    S->>S: Store Assignment
    Stu->>St: View Assignments
    St->>S: Fetch Assignment Data
    S->>St: Return Assignment Details
    St->>Stu: Display Assignment
    Stu->>St: Submit Assignment
    St->>St: Track Progress
```

## 🛡️ Security Features

### Service-to-Service Security

1. **API Key Authentication**: Each service has unique API keys
2. **Request Signing**: Cryptographic signatures prevent tampering
3. **Timestamp Validation**: Prevents replay attacks
4. **Service Allowlisting**: Only authorized services can communicate

### Data Protection

1. **Encrypted Communication**: All inter-service calls use HTTPS
2. **Audit Logging**: All cross-service actions are logged
3. **Access Control**: Services only access authorized endpoints
4. **Data Validation**: All incoming data is validated

## 📡 Service Endpoints

### Admin Service Endpoints

```
GET  /api/service/health          - Service health check
GET  /api/service/users           - Get user management records
POST /api/service/users           - Create user management record
```

### Staff Service Endpoints

```
GET  /api/service/health          - Service health check
GET  /api/service/assignments     - Get assignments (filtered)
GET  /api/leads                   - Get leads
POST /api/leads                   - Create lead
POST /api/leads/{id}/convert      - Convert lead to student
```

### Student Service Endpoints

```
GET  /api/service/health          - Service health check
GET  /api/service/students        - Get students
POST /api/service/students        - Create student
POST /api/schedule/sync           - Sync schedule from staff service
```

## 🧪 Testing Integration

### Running Integration Tests

```bash
# Install dependencies
npm install

# Set environment variables
export SERVICE_API_KEY="your-api-key"
export ADMIN_SERVICE_URL="http://localhost:3001"
export STAFF_SERVICE_URL="http://localhost:3002"
export STUDENT_SERVICE_URL="http://localhost:3003"

# Run integration tests
node integration-test.js
```

### Test Coverage

The integration tests cover:

- ✅ Service health checks
- ✅ Lead to student conversion workflow
- ✅ Course and group creation
- ✅ Payment recording and verification
- ✅ Schedule synchronization
- ✅ Progress tracking updates

## 🔧 Configuration

### Environment Variables

Each service requires these environment variables:

```bash
# Service URLs
ADMIN_SERVICE_URL="https://crm-admin-service.vercel.app"
STAFF_SERVICE_URL="https://crm-staff-service.vercel.app"
STUDENT_SERVICE_URL="https://crm-student-service.vercel.app"

# Authentication
SERVICE_API_KEY="your-secure-api-key"
SERVICE_NAME="crm-service-name"

# Database connections
ADMIN_DATABASE_URL="postgresql://..."
STAFF_DATABASE_URL="postgresql://..."
STUDENT_DATABASE_URL="postgresql://..."
```

### Service Configuration

```javascript
// Service authentication configuration
const serviceAuthConfig = {
  serviceName: 'crm-staff-service',
  apiKey: process.env.SERVICE_API_KEY,
  allowedServices: [
    'crm-admin-service',
    'crm-student-service',
    'crm-staff-service'
  ],
};
```

## 🚀 Deployment Considerations

### Production Setup

1. **Service Discovery**: Use environment variables for service URLs
2. **Load Balancing**: Configure load balancers for each service
3. **Health Monitoring**: Monitor service health endpoints
4. **Error Handling**: Implement circuit breakers for resilience

### Scaling

1. **Horizontal Scaling**: Each service can be scaled independently
2. **Database Separation**: Each service has its own database
3. **Caching**: Implement caching for frequently accessed data
4. **Rate Limiting**: Configure rate limits for inter-service calls

## 📈 Monitoring and Observability

### Health Checks

Each service provides health check endpoints:

```javascript
GET /api/health
{
  "status": "healthy",
  "service": "crm-staff-service",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "database": "connected",
  "version": "1.0.0"
}
```

### Audit Logging

All inter-service communications are logged:

```javascript
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "sourceService": "crm-staff-service",
  "targetService": "crm-student-service",
  "action": "CREATE_STUDENT",
  "success": true,
  "duration": 150
}
```

---

## 🎯 Next Steps

1. **Real-time Notifications**: Implement WebSocket connections for real-time updates
2. **Event Sourcing**: Add event-driven architecture for better data consistency
3. **API Gateway**: Implement a central API gateway for external access
4. **Monitoring Dashboard**: Create a unified monitoring dashboard
5. **Performance Optimization**: Implement caching and optimization strategies

For more information, see the individual service documentation and the main project README.
