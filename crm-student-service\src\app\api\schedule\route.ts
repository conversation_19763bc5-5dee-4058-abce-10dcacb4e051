// Student Schedule API - Student Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

// GET /api/schedule - Get student's class schedule
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get student record
    const student = await prisma.student.findFirst({
      where: { studentUserId: session.user.id },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    const where: any = { 
      studentId: student.id,
      isActive: true,
    };

    if (startDate || endDate) {
      where.startDate = {};
      if (startDate) where.startDate.gte = new Date(startDate);
      if (endDate) where.startDate.lte = new Date(endDate);
    }

    const schedules = await prisma.studentSchedule.findMany({
      where,
      orderBy: { startDate: "asc" },
    });

    // Group schedules by week for easier display
    const weeklySchedule = groupSchedulesByWeek(schedules);

    return NextResponse.json({
      success: true,
      data: {
        student: {
          id: student.id,
          studentId: student.studentId,
          currentLevel: student.currentLevel,
        },
        schedules,
        weeklySchedule,
        totalClasses: schedules.length,
      },
    });
  } catch (error) {
    console.error("Error fetching schedule:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to group schedules by week
function groupSchedulesByWeek(schedules: any[]) {
  const weeks: Record<string, any[]> = {};

  schedules.forEach(schedule => {
    const startDate = new Date(schedule.startDate);
    const weekStart = new Date(startDate);
    weekStart.setDate(startDate.getDate() - startDate.getDay()); // Start of week (Sunday)
    
    const weekKey = weekStart.toISOString().split('T')[0];
    
    if (!weeks[weekKey]) {
      weeks[weekKey] = [];
    }
    
    weeks[weekKey].push(schedule);
  });

  return weeks;
}
