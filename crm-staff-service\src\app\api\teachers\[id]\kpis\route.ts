// Teacher KPI Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { KPIMetricName } from "@prisma/client";
import { z } from "zod";

const createKPISchema = z.object({
  metricName: z.nativeEnum(KPIMetricName),
  metricValue: z.number(),
  targetValue: z.number().optional(),
  periodStart: z.string().transform((str) => new Date(str)),
  periodEnd: z.string().transform((str) => new Date(str)),
});

const updateKPISchema = z.object({
  metricValue: z.number().optional(),
  targetValue: z.number().optional(),
  periodStart: z.string().transform((str) => new Date(str)).optional(),
  periodEnd: z.string().transform((str) => new Date(str)).optional(),
});

// GET /api/teachers/[id]/kpis - Get teacher KPIs
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const metricName = searchParams.get("metricName") as KPIMetricName | null;
    const periodStart = searchParams.get("periodStart");
    const periodEnd = searchParams.get("periodEnd");

    const skip = (page - 1) * limit;

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: params.id },
      include: {
        staffUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!teacher) {
      return NextResponse.json({ error: "Teacher not found" }, { status: 404 });
    }

    // Check permissions - teachers can only view their own KPIs
    if (session.user.role === "teacher" && session.user.id !== teacher.staffUser.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const where: any = { teacherId: params.id };
    if (metricName) where.metricName = metricName;
    if (periodStart || periodEnd) {
      where.periodStart = {};
      if (periodStart) where.periodStart.gte = new Date(periodStart);
      if (periodEnd) where.periodStart.lte = new Date(periodEnd);
    }

    const [kpis, total] = await Promise.all([
      prisma.teacherKPI.findMany({
        where,
        skip,
        take: limit,
        orderBy: { calculatedAt: "desc" },
      }),
      prisma.teacherKPI.count({ where }),
    ]);

    // Calculate summary statistics
    const summary = await calculateKPISummary(params.id);

    return NextResponse.json({
      success: true,
      data: {
        teacher: {
          id: teacher.id,
          name: `${teacher.staffUser.firstName} ${teacher.staffUser.lastName}`,
          email: teacher.staffUser.email,
          specialization: teacher.specialization,
          kpiScore: teacher.kpiScore,
        },
        kpis,
        summary,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching teacher KPIs:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/teachers/[id]/kpis - Create new KPI record
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create KPIs
    if (!["manager"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createKPISchema.parse(body);

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: params.id },
    });

    if (!teacher) {
      return NextResponse.json({ error: "Teacher not found" }, { status: 404 });
    }

    const kpi = await prisma.teacherKPI.create({
      data: {
        teacherId: params.id,
        ...validatedData,
      },
    });

    // Update teacher's overall KPI score
    await updateTeacherKPIScore(params.id);

    return NextResponse.json({
      success: true,
      data: kpi,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating KPI:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to calculate KPI summary
async function calculateKPISummary(teacherId: string) {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentKPIs = await prisma.teacherKPI.findMany({
    where: {
      teacherId,
      calculatedAt: {
        gte: thirtyDaysAgo,
      },
    },
  });

  const summary: Record<string, any> = {};

  // Group by metric name and calculate averages
  const metricGroups = recentKPIs.reduce((acc, kpi) => {
    if (!acc[kpi.metricName]) {
      acc[kpi.metricName] = [];
    }
    acc[kpi.metricName].push(Number(kpi.metricValue));
    return acc;
  }, {} as Record<string, number[]>);

  Object.entries(metricGroups).forEach(([metricName, values]) => {
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;
    const latest = values[values.length - 1];
    const trend = values.length > 1 ? latest - values[0] : 0;

    summary[metricName] = {
      average: Math.round(average * 100) / 100,
      latest,
      trend: Math.round(trend * 100) / 100,
      count: values.length,
    };
  });

  return summary;
}

// Helper function to update teacher's overall KPI score
async function updateTeacherKPIScore(teacherId: string) {
  const recentKPIs = await prisma.teacherKPI.findMany({
    where: {
      teacherId,
      calculatedAt: {
        gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Last 90 days
      },
    },
  });

  if (recentKPIs.length === 0) return;

  // Calculate weighted average based on metric importance
  const weights: Record<string, number> = {
    student_retention_rate: 0.25,
    student_progress_score: 0.25,
    class_attendance_rate: 0.20,
    student_satisfaction_rating: 0.15,
    assignment_completion_rate: 0.10,
    professional_development_hours: 0.05,
  };

  let totalScore = 0;
  let totalWeight = 0;

  Object.entries(weights).forEach(([metricName, weight]) => {
    const metricKPIs = recentKPIs.filter(kpi => kpi.metricName === metricName);
    if (metricKPIs.length > 0) {
      const avgValue = metricKPIs.reduce((sum, kpi) => sum + Number(kpi.metricValue), 0) / metricKPIs.length;
      totalScore += avgValue * weight;
      totalWeight += weight;
    }
  });

  const overallScore = totalWeight > 0 ? totalScore / totalWeight : 0;

  await prisma.teacher.update({
    where: { id: teacherId },
    data: { kpiScore: overallScore },
  });
}
