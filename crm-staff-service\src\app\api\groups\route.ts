// Groups Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { GroupStatus } from "@prisma/client";
import { z } from "zod";

const createGroupSchema = z.object({
  groupName: z.string().min(1, "Group name is required"),
  courseId: z.string().min(1, "Course ID is required"),
  teacherId: z.string().min(1, "Teacher ID is required"),
  cabinetId: z.string().min(1, "Cabinet ID is required"),
  maxStudents: z.number().positive("Max students must be positive"),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)).optional(),
  schedule: z.object({
    days: z.array(z.string()),
    startTime: z.string(),
    endTime: z.string(),
  }),
});

const updateGroupSchema = z.object({
  groupName: z.string().min(1).optional(),
  teacherId: z.string().min(1).optional(),
  cabinetId: z.string().min(1).optional(),
  maxStudents: z.number().positive().optional(),
  startDate: z.string().transform((str) => new Date(str)).optional(),
  endDate: z.string().transform((str) => new Date(str)).optional(),
  schedule: z.object({
    days: z.array(z.string()),
    startTime: z.string(),
    endTime: z.string(),
  }).optional(),
  status: z.nativeEnum(GroupStatus).optional(),
});

// GET /api/groups - List groups
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") as GroupStatus | null;
    const courseId = searchParams.get("courseId");
    const teacherId = searchParams.get("teacherId");

    const skip = (page - 1) * limit;

    const where: any = {};
    if (status) where.status = status;
    if (courseId) where.courseId = courseId;
    if (teacherId) where.teacherId = teacherId;

    // If user is a teacher, only show their groups
    if (session.user.role === "teacher" && session.user.teacherId) {
      where.teacherId = session.user.teacherId;
    }

    const [groups, total] = await Promise.all([
      prisma.group.findMany({
        where,
        skip,
        take: limit,
        include: {
          course: {
            select: {
              courseCode: true,
              title: true,
              level: true,
            },
          },
          teacher: {
            include: {
              staffUser: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          cabinet: {
            select: {
              cabinetNumber: true,
              cabinetName: true,
              capacity: true,
            },
          },
          createdByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              enrollments: true,
              assignments: true,
              resources: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.group.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        groups,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching groups:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/groups - Create new group
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create groups
    if (!["manager", "teacher"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createGroupSchema.parse(body);

    // Verify that the course exists
    const course = await prisma.course.findUnique({
      where: { id: validatedData.courseId },
    });

    if (!course) {
      return NextResponse.json(
        { error: "Course not found" },
        { status: 404 }
      );
    }

    // Verify that the teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id: validatedData.teacherId },
    });

    if (!teacher) {
      return NextResponse.json(
        { error: "Teacher not found" },
        { status: 404 }
      );
    }

    // Verify that the cabinet exists and is available
    const cabinet = await prisma.cabinet.findUnique({
      where: { id: validatedData.cabinetId },
    });

    if (!cabinet) {
      return NextResponse.json(
        { error: "Cabinet not found" },
        { status: 404 }
      );
    }

    if (cabinet.status !== "available") {
      return NextResponse.json(
        { error: "Cabinet is not available" },
        { status: 400 }
      );
    }

    const group = await prisma.group.create({
      data: {
        ...validatedData,
        createdBy: session.user.id,
      },
      include: {
        course: {
          select: {
            courseCode: true,
            title: true,
            level: true,
          },
        },
        teacher: {
          include: {
            staffUser: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        cabinet: {
          select: {
            cabinetNumber: true,
            cabinetName: true,
            capacity: true,
          },
        },
        createdByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Update cabinet status to occupied
    await prisma.cabinet.update({
      where: { id: validatedData.cabinetId },
      data: { status: "occupied" },
    });

    return NextResponse.json({
      success: true,
      data: group,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating group:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
