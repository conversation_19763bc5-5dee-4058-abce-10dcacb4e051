import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

// Validation schema for progress update
const updateProgressSchema = z.object({
  assignmentId: z.string().min(1),
  progressPercentage: z.number().min(0).max(100),
  feedback: z.string().optional(),
});

// GET /api/progress - Get student's overall progress
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get student record
    const student = await prisma.student.findFirst({
      where: { studentUserId: session.user.id },
      include: {
        studentUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    // Get all progress records
    const progressRecords = await prisma.studentProgress.findMany({
      where: { studentId: student.id },
      orderBy: { createdAt: 'desc' },
    });

    // Get all submissions
    const submissions = await prisma.assignmentSubmission.findMany({
      where: { studentId: student.id },
      orderBy: { submittedAt: 'desc' },
    });

    // Calculate overall statistics
    const totalAssignments = progressRecords.length;
    const completedAssignments = progressRecords.filter(p => p.progressPercentage === 100).length;
    const gradedAssignments = progressRecords.filter(p => p.grade !== null).length;
    const averageProgress = totalAssignments > 0
      ? progressRecords.reduce((sum, p) => sum + p.progressPercentage, 0) / totalAssignments
      : 0;

    // Calculate grade statistics
    const gradedRecords = progressRecords.filter(p => p.grade !== null);
    const gradeDistribution: Record<string, number> = {};
    gradedRecords.forEach(record => {
      if (record.grade) {
        gradeDistribution[record.grade] = (gradeDistribution[record.grade] || 0) + 1;
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        student: {
          id: student.id,
          studentId: student.studentId,
          name: `${student.studentUser.firstName} ${student.studentUser.lastName}`,
          email: student.studentUser.email,
          currentLevel: student.currentLevel,
          status: student.status,
          enrollmentDate: student.enrollmentDate,
        },
        statistics: {
          totalAssignments,
          completedAssignments,
          gradedAssignments,
          averageProgress: Math.round(averageProgress * 100) / 100,
          completionRate: totalAssignments > 0 ? Math.round((completedAssignments / totalAssignments) * 100) : 0,
        },
        gradeDistribution,
        progressRecords: progressRecords.slice(0, 20), // Latest 20 records
      },
    });
  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = updateProgressSchema.parse(body);

    // TODO: Get the current student ID from session/auth
    const studentId = 'temp-student-id'; // This should come from authentication

    const progress = await prisma.studentProgress.upsert({
      where: {
        studentId_assignmentId: {
          studentId,
          assignmentId: validatedData.assignmentId,
        },
      },
      update: {
        progressPercentage: validatedData.progressPercentage,
        feedback: validatedData.feedback,
      },
      create: {
        studentId,
        assignmentId: validatedData.assignmentId,
        progressPercentage: validatedData.progressPercentage,
        feedback: validatedData.feedback,
      },
    });

    return NextResponse.json({
      success: true,
      data: progress,
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid progress data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error updating progress:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'UPDATE_PROGRESS_ERROR',
        message: 'Failed to update progress',
      },
    }, { status: 500 });
  }
}
