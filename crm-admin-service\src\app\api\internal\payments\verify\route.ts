// Internal API for payment verification - Admin Service
// Called by other services to verify payment records

import { NextRequest, NextResponse } from "next/server";
import { authManager } from "@/lib/service-clients";
import { prisma } from "@/lib/db";
import { PaymentStatus } from "@prisma/client";
import { z } from "zod";

const verifyPaymentSchema = z.object({
  studentId: z.string().min(1, "Student ID is required"),
  amount: z.number().positive("Amount must be positive"),
  courseId: z.string().optional(),
  description: z.string().optional(),
});

// POST /api/internal/payments/verify - Verify payment for a student
export async function POST(request: NextRequest) {
  try {
    // Authenticate the calling service
    const serviceToken = await authManager.authenticateService(request);
    if (!serviceToken) {
      return NextResponse.json({ error: "Unauthorized service" }, { status: 401 });
    }

    // Check if service has permission to verify payments
    if (!serviceToken.permissions.includes('write') && !serviceToken.permissions.includes('payment:verify')) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = verifyPaymentSchema.parse(body);

    // Look for existing payment records for this student
    const existingPayments = await prisma.paymentRecord.findMany({
      where: {
        studentId: validatedData.studentId,
        status: PaymentStatus.verified,
      },
      orderBy: { paymentDate: 'desc' },
    });

    // Calculate total verified payments
    const totalPaid = existingPayments.reduce((sum, payment) => sum + Number(payment.amount), 0);

    // Check if student has sufficient payment for the requested amount
    const hasValidPayment = totalPaid >= validatedData.amount;

    // Get payment history
    const paymentHistory = existingPayments.slice(0, 5).map(payment => ({
      id: payment.id,
      amount: payment.amount,
      paymentDate: payment.paymentDate,
      paymentMethod: payment.paymentMethod,
      description: payment.description,
    }));

    const response = {
      success: true,
      data: {
        studentId: validatedData.studentId,
        hasValidPayment,
        totalPaid,
        requestedAmount: validatedData.amount,
        balance: totalPaid - validatedData.amount,
        paymentHistory,
        verifiedAt: new Date(),
        verifiedBy: serviceToken.serviceName,
      },
    };

    // Log the verification request
    console.log(`Payment verification request from ${serviceToken.serviceName} for student ${validatedData.studentId}: ${hasValidPayment ? 'APPROVED' : 'DENIED'}`);

    return NextResponse.json(response);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
