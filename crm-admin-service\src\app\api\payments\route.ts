import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { PaymentMethod, PaymentStatus, TransactionType } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
const createPaymentSchema = z.object({
  studentId: z.string().min(1, "Student ID is required"),
  amount: z.number().positive("Amount must be positive"),
  paymentDate: z.string().transform((str) => new Date(str)),
  paymentMethod: z.nativeEnum(PaymentMethod),
  description: z.string().optional(),
  notes: z.string().optional(),
});

const updatePaymentSchema = z.object({
  amount: z.number().positive().optional(),
  paymentDate: z.string().transform((str) => new Date(str)).optional(),
  paymentMethod: z.nativeEnum(PaymentMethod).optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(PaymentStatus).optional(),
});

// GET /api/payments - List payment records
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const studentId = searchParams.get('studentId');
    const status = searchParams.get('status') as PaymentStatus | null;

    const skip = (page - 1) * limit;

    const where: any = {};
    if (studentId) where.studentId = studentId;
    if (status) where.status = status;

    const [payments, total] = await Promise.all([
      prisma.paymentRecord.findMany({
        where,
        skip,
        take: limit,
        include: {
          recordedByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          verifiedByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.paymentRecord.count({ where }),
    ]);

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: 'VIEW_PAYMENTS',
        resourceType: 'PAYMENT_RECORD',
        newValues: { page, limit, filters: { studentId, status } },
        ipAddress: '127.0.0.1', // TODO: Get real IP
        userAgent: request.headers.get('user-agent') || 'Unknown',
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        payments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching payments:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'FETCH_PAYMENTS_ERROR',
        message: 'Failed to fetch payment records',
      },
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createPaymentSchema.parse(body);

    // TODO: Get the current user ID from session/auth
    const recordedBy = 'temp-admin-user-id'; // This should come from authentication

    const payment = await prisma.paymentRecord.create({
      data: {
        studentId: validatedData.studentId,
        amount: validatedData.amount,
        paymentDate: validatedData.paymentDate,
        paymentMethod: validatedData.paymentMethod,
        description: validatedData.description,
        notes: validatedData.notes,
        recordedBy,
      },
      include: {
        recordedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // TODO: Create audit log entry
    await prisma.auditLog.create({
      data: {
        userId: recordedBy,
        action: 'CREATE_PAYMENT_RECORD',
        resourceType: 'PaymentRecord',
        resourceId: payment.id,
        newValues: payment,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      data: payment,
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid payment data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error creating payment:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'CREATE_PAYMENT_ERROR',
        message: 'Failed to create payment record',
      },
    }, { status: 500 });
  }
}
