// Internal API for user management - Admin Service
// Called by other services for user operations

import { NextRequest, NextResponse } from "next/server";
import { authManager } from "@/lib/service-clients";
import { prisma } from "@/lib/db";
import { AdminRole } from "@prisma/client";
import { z } from "zod";
import bcrypt from "bcryptjs";

const createUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  role: z.nativeEnum(AdminRole),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  phone: z.string().optional(),
  requestingService: z.string().min(1, "Requesting service is required"),
});

// GET /api/internal/users - List users (for other services)
export async function GET(request: NextRequest) {
  try {
    // Authenticate the calling service
    const serviceToken = await authManager.authenticateService(request);
    if (!serviceToken) {
      return NextResponse.json({ error: "Unauthorized service" }, { status: 401 });
    }

    // Check if service has permission to read users
    if (!serviceToken.permissions.includes('read') && !serviceToken.permissions.includes('user:read')) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const role = searchParams.get("role") as AdminRole | null;
    const isActive = searchParams.get("isActive");

    const where: any = {};
    if (role) where.role = role;
    if (isActive !== null) where.isActive = isActive === "true";

    const users = await prisma.adminUser.findMany({
      where,
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        phone: true,
        isActive: true,
        createdAt: true,
        lastLogin: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json({
      success: true,
      data: users,
      requestedBy: serviceToken.serviceName,
    });
  } catch (error) {
    console.error("Error fetching users for service:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/internal/users - Create user (for other services)
export async function POST(request: NextRequest) {
  try {
    // Authenticate the calling service
    const serviceToken = await authManager.authenticateService(request);
    if (!serviceToken) {
      return NextResponse.json({ error: "Unauthorized service" }, { status: 401 });
    }

    // Check if service has permission to create users
    if (!serviceToken.permissions.includes('write') && !serviceToken.permissions.includes('user:create')) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    // Check if email already exists
    const existingUser = await prisma.adminUser.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const passwordHash = await bcrypt.hash(validatedData.password, 12);

    const { password, requestingService, ...userData } = validatedData;
    const newUser = await prisma.adminUser.create({
      data: {
        ...userData,
        passwordHash,
      },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        phone: true,
        isActive: true,
        createdAt: true,
      },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: 'system',
        action: 'CREATE_USER_VIA_SERVICE',
        resourceType: 'ADMIN_USER',
        resourceId: newUser.id,
        newValues: { 
          ...userData, 
          passwordHash: '[REDACTED]',
          createdByService: serviceToken.serviceName,
          requestingService,
        },
        ipAddress: '127.0.0.1',
        userAgent: `Service: ${serviceToken.serviceName}`,
      },
    });

    return NextResponse.json({
      success: true,
      data: newUser,
      createdByService: serviceToken.serviceName,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating user via service:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
