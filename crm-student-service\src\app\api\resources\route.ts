// Student Resources API - Student Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

// GET /api/resources - Get student's accessible resources
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get student record
    const student = await prisma.student.findFirst({
      where: { studentUserId: session.user.id },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    const skip = (page - 1) * limit;

    // Get resource access history
    const [resourceAccess, total] = await Promise.all([
      prisma.studentResourceAccess.findMany({
        where: { studentId: student.id },
        skip,
        take: limit,
        orderBy: { accessedAt: "desc" },
      }),
      prisma.studentResourceAccess.count({ where: { studentId: student.id } }),
    ]);

    // Group resources by resourceId to get unique resources
    const uniqueResources = resourceAccess.reduce((acc, access) => {
      if (!acc[access.resourceId]) {
        acc[access.resourceId] = {
          resourceId: access.resourceId,
          lastAccessed: access.accessedAt,
          totalAccesses: 1,
          totalDuration: access.accessDuration || 0,
        };
      } else {
        acc[access.resourceId].totalAccesses += 1;
        acc[access.resourceId].totalDuration += access.accessDuration || 0;
        if (access.accessedAt > acc[access.resourceId].lastAccessed) {
          acc[access.resourceId].lastAccessed = access.accessedAt;
        }
      }
      return acc;
    }, {} as Record<string, any>);

    const resourceSummary = Object.values(uniqueResources);

    return NextResponse.json({
      success: true,
      data: {
        student: {
          id: student.id,
          studentId: student.studentId,
          currentLevel: student.currentLevel,
        },
        resourceAccess,
        resourceSummary,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching resources:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/resources - Track resource access
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { resourceId, accessDuration } = body;

    if (!resourceId) {
      return NextResponse.json(
        { error: "Resource ID is required" },
        { status: 400 }
      );
    }

    // Get student record
    const student = await prisma.student.findFirst({
      where: { studentUserId: session.user.id },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    // Record resource access
    const resourceAccess = await prisma.studentResourceAccess.create({
      data: {
        studentId: student.id,
        resourceId,
        accessDuration: accessDuration || null,
      },
    });

    return NextResponse.json({
      success: true,
      data: resourceAccess,
      message: "Resource access recorded",
    });
  } catch (error) {
    console.error("Error recording resource access:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
