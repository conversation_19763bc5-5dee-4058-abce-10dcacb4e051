{"name": "crm-shared-types", "version": "1.0.0", "description": "Shared TypeScript types and interfaces for Innovative Centre CRM", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist/**/*"], "scripts": {"dev": "next dev", "build": "next build && npm run build:types", "build:types": "node build.js", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@types/jsonwebtoken": "^9.0.10", "jsonwebtoken": "^9.0.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}, "keywords": ["crm", "typescript", "types", "interfaces", "education", "innovative-centre"], "author": "Innovative Centre", "license": "MIT"}