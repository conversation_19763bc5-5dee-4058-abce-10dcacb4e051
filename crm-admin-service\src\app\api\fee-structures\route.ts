// Fee Structures API - Admin Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { FeeType } from "@prisma/client";
import { z } from "zod";

const createFeeStructureSchema = z.object({
  courseId: z.string().min(1, "Course ID is required"),
  feeType: z.nativeEnum(FeeType),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().length(3, "Currency must be 3 characters").default("USD"),
  effectiveDate: z.string().transform((str) => new Date(str)),
  expiryDate: z.string().transform((str) => new Date(str)).optional(),
});

const updateFeeStructureSchema = z.object({
  amount: z.number().positive().optional(),
  currency: z.string().length(3).optional(),
  effectiveDate: z.string().transform((str) => new Date(str)).optional(),
  expiryDate: z.string().transform((str) => new Date(str)).optional(),
  isActive: z.boolean().optional(),
});

// GET /api/fee-structures - List fee structures
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const courseId = searchParams.get("courseId");
    const feeType = searchParams.get("feeType") as FeeType | null;
    const isActive = searchParams.get("isActive");

    const skip = (page - 1) * limit;

    const where: any = {};
    if (courseId) where.courseId = courseId;
    if (feeType) where.feeType = feeType;
    if (isActive !== null) where.isActive = isActive === "true";

    const [feeStructures, total] = await Promise.all([
      prisma.feeStructure.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.feeStructure.count({ where }),
    ]);

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "VIEW_FEE_STRUCTURES",
        resourceType: "FEE_STRUCTURE",
        newValues: { page, limit, filters: { courseId, feeType, isActive } },
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        feeStructures,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching fee structures:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/fee-structures - Create new fee structure
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create fee structures
    if (!["admin", "accounting"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createFeeStructureSchema.parse(body);

    const feeStructure = await prisma.feeStructure.create({
      data: {
        ...validatedData,
        createdBy: session.user.id,
      },
      include: {
        createdByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "CREATE_FEE_STRUCTURE",
        resourceType: "FEE_STRUCTURE",
        resourceId: feeStructure.id,
        newValues: validatedData,
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      data: feeStructure,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating fee structure:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
