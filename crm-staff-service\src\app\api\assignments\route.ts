// Assignment Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { AssignmentStatus } from "@prisma/client";
import { z } from "zod";

const createAssignmentSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  instructions: z.string().optional(),
  groupId: z.string().min(1, "Group ID is required"),
  dueDate: z.string().transform((str) => new Date(str)),
  maxPoints: z.number().positive("Max points must be positive").optional(),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string().url(),
    fileSize: z.number().optional(),
  })).optional(),
});

const updateAssignmentSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  instructions: z.string().optional(),
  dueDate: z.string().transform((str) => new Date(str)).optional(),
  maxPoints: z.number().positive().optional(),
  status: z.nativeEnum(AssignmentStatus).optional(),
  attachments: z.array(z.object({
    fileName: z.string(),
    fileUrl: z.string().url(),
    fileSize: z.number().optional(),
  })).optional(),
});

// GET /api/assignments - List assignments
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const groupId = searchParams.get("groupId");
    const status = searchParams.get("status") as AssignmentStatus | null;

    const skip = (page - 1) * limit;

    const where: any = {};
    if (groupId) where.groupId = groupId;
    if (status) where.status = status;

    // If user is a teacher, only show assignments for their groups
    if (session.user.role === "teacher" && session.user.teacherId) {
      const teacherGroups = await prisma.group.findMany({
        where: { teacherId: session.user.teacherId },
        select: { id: true },
      });
      const groupIds = teacherGroups.map(g => g.id);
      where.groupId = { in: groupIds };
    }

    const [assignments, total] = await Promise.all([
      prisma.assignment.findMany({
        where,
        skip,
        take: limit,
        include: {
          group: {
            include: {
              course: {
                select: {
                  courseCode: true,
                  title: true,
                },
              },
              teacher: {
                include: {
                  staffUser: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          createdByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              submissions: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.assignment.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        assignments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching assignments:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/assignments - Create new assignment
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create assignments
    if (!["teacher", "manager"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createAssignmentSchema.parse(body);

    // Verify that the group exists and user has access
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        teacher: {
          include: {
            staffUser: true,
          },
        },
      },
    });

    if (!group) {
      return NextResponse.json({ error: "Group not found" }, { status: 404 });
    }

    // If user is a teacher, verify they own this group
    if (session.user.role === "teacher" && group.teacher.staffUser.id !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const assignment = await prisma.assignment.create({
      data: {
        ...validatedData,
        createdBy: session.user.id,
        attachments: validatedData.attachments || [],
      },
      include: {
        group: {
          include: {
            course: {
              select: {
                courseCode: true,
                title: true,
              },
            },
            teacher: {
              include: {
                staffUser: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        createdByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // TODO: Notify students about new assignment
    // This would involve calling the student service to create notifications

    return NextResponse.json({
      success: true,
      data: assignment,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating assignment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
