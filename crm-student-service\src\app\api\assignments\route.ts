// Student Assignments API - Student Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { SubmissionStatus } from "@prisma/client";

// GET /api/assignments - List student's assignments
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    const skip = (page - 1) * limit;

    // Get student record
    const student = await prisma.student.findFirst({
      where: { studentUserId: session.user.id },
    });

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 });
    }

    const where: any = { studentId: student.id };
    if (status) where.status = status as SubmissionStatus;

    // Get assignments with progress and submissions
    const [progress, submissions, total] = await Promise.all([
      prisma.studentProgress.findMany({
        where: { studentId: student.id },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.assignmentSubmission.findMany({
        where,
        skip,
        take: limit,
        orderBy: { submittedAt: "desc" },
      }),
      prisma.studentProgress.count({ where: { studentId: student.id } }),
    ]);

    // Combine progress and submission data
    const assignments = progress.map(prog => {
      const submission = submissions.find(sub => sub.assignmentId === prog.assignmentId);
      return {
        assignmentId: prog.assignmentId,
        progress: {
          percentage: prog.progressPercentage,
          grade: prog.grade,
          feedback: prog.feedback,
          submittedAt: prog.submittedAt,
          gradedAt: prog.gradedAt,
        },
        submission: submission ? {
          id: submission.id,
          content: submission.submissionContent,
          fileUrl: submission.fileUrl,
          submittedAt: submission.submittedAt,
          status: submission.status,
          teacherFeedback: submission.teacherFeedback,
          grade: submission.grade,
          gradedAt: submission.gradedAt,
        } : null,
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        assignments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching assignments:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
