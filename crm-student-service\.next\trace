[{"name":"hot-reloader","duration":274,"timestamp":287113492275,"id":3,"tags":{"version":"15.3.4"},"startTime":1750489167969,"traceId":"901c6e83d477b378"},{"name":"start","duration":25,"timestamp":287113493933,"id":4,"parentId":3,"tags":{},"startTime":1750489167970,"traceId":"901c6e83d477b378"},{"name":"get-version-info","duration":877086,"timestamp":287113494050,"id":5,"parentId":4,"tags":{},"startTime":1750489167970,"traceId":"901c6e83d477b378"},{"name":"clean","duration":318395,"timestamp":287114371527,"id":6,"parentId":4,"tags":{},"startTime":1750489168848,"traceId":"901c6e83d477b378"},{"name":"get-page-paths","duration":4183,"timestamp":287114697536,"id":8,"parentId":7,"tags":{},"startTime":1750489169174,"traceId":"901c6e83d477b378"},{"name":"create-pages-mapping","duration":862,"timestamp":287114701817,"id":9,"parentId":7,"tags":{},"startTime":1750489169178,"traceId":"901c6e83d477b378"},{"name":"create-entrypoints","duration":15565,"timestamp":287114702778,"id":10,"parentId":7,"tags":{},"startTime":1750489169179,"traceId":"901c6e83d477b378"},{"name":"generate-webpack-config","duration":1084037,"timestamp":287114718765,"id":11,"parentId":7,"tags":{},"startTime":1750489169195,"traceId":"901c6e83d477b378"},{"name":"get-webpack-config","duration":1107971,"timestamp":287114694911,"id":7,"parentId":4,"tags":{},"startTime":1750489169171,"traceId":"901c6e83d477b378"},{"name":"make","duration":3502,"timestamp":287116069252,"id":13,"parentId":12,"tags":{},"startTime":1750489170546,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":1395,"timestamp":287116081173,"id":15,"parentId":14,"tags":{},"startTime":1750489170558,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":56,"timestamp":287116082860,"id":17,"parentId":14,"tags":{},"startTime":1750489170559,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":410,"timestamp":287116083077,"id":18,"parentId":14,"tags":{},"startTime":1750489170560,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":294,"timestamp":287116083673,"id":19,"parentId":14,"tags":{},"startTime":1750489170560,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":267,"timestamp":287116084384,"id":20,"parentId":14,"tags":{},"startTime":1750489170561,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":2257,"timestamp":287116082737,"id":16,"parentId":14,"tags":{},"startTime":1750489170559,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":226,"timestamp":287116087456,"id":21,"parentId":14,"tags":{},"startTime":1750489170564,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":552,"timestamp":287116087775,"id":22,"parentId":14,"tags":{},"startTime":1750489170564,"traceId":"901c6e83d477b378"},{"name":"hash","duration":1402,"timestamp":287116089049,"id":23,"parentId":14,"tags":{},"startTime":1750489170565,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":401,"timestamp":287116090439,"id":24,"parentId":14,"tags":{},"startTime":1750489170567,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":222,"timestamp":287116090753,"id":25,"parentId":14,"tags":{},"startTime":1750489170567,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":473,"timestamp":287116091014,"id":26,"parentId":14,"tags":{},"startTime":1750489170567,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-generateClientManifest","duration":9110,"timestamp":287116377071,"id":28,"parentId":12,"tags":{},"startTime":1750489170854,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":11436,"timestamp":287116374834,"id":27,"parentId":12,"tags":{},"startTime":1750489170851,"traceId":"901c6e83d477b378"},{"name":"seal","duration":313928,"timestamp":287116079395,"id":14,"parentId":12,"tags":{},"startTime":1750489170556,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":341350,"timestamp":287116052868,"id":12,"parentId":3,"tags":{"name":"client"},"startTime":1750489170529,"traceId":"901c6e83d477b378"},{"name":"emit","duration":154011,"timestamp":287116398781,"id":29,"parentId":3,"tags":{},"startTime":1750489170875,"traceId":"901c6e83d477b378"},{"name":"make","duration":6434,"timestamp":287116625347,"id":31,"parentId":30,"tags":{},"startTime":1750489171102,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":105,"timestamp":287116633032,"id":33,"parentId":32,"tags":{},"startTime":1750489171109,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":18,"timestamp":287116633216,"id":35,"parentId":32,"tags":{},"startTime":1750489171110,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":2526,"timestamp":287116633381,"id":36,"parentId":32,"tags":{},"startTime":1750489171110,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":63,"timestamp":287116636117,"id":37,"parentId":32,"tags":{},"startTime":1750489171113,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":44,"timestamp":287116636451,"id":38,"parentId":32,"tags":{},"startTime":1750489171113,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":3507,"timestamp":287116633192,"id":34,"parentId":32,"tags":{},"startTime":1750489171110,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":40,"timestamp":287116637255,"id":39,"parentId":32,"tags":{},"startTime":1750489171114,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":29,"timestamp":287116637336,"id":40,"parentId":32,"tags":{},"startTime":1750489171114,"traceId":"901c6e83d477b378"},{"name":"hash","duration":319,"timestamp":287116637562,"id":41,"parentId":32,"tags":{},"startTime":1750489171114,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":177,"timestamp":287116637878,"id":42,"parentId":32,"tags":{},"startTime":1750489171114,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":50,"timestamp":287116638021,"id":43,"parentId":32,"tags":{},"startTime":1750489171114,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":50,"timestamp":287116638088,"id":44,"parentId":32,"tags":{},"startTime":1750489171115,"traceId":"901c6e83d477b378"},{"name":"seal","duration":9600,"timestamp":287116632870,"id":32,"parentId":30,"tags":{},"startTime":1750489171109,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":21117,"timestamp":287116621899,"id":30,"parentId":3,"tags":{"name":"server"},"startTime":1750489171098,"traceId":"901c6e83d477b378"},{"name":"emit","duration":55238,"timestamp":287116643334,"id":45,"parentId":3,"tags":{},"startTime":1750489171120,"traceId":"901c6e83d477b378"},{"name":"make","duration":907,"timestamp":287116723189,"id":47,"parentId":46,"tags":{},"startTime":1750489171200,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":94,"timestamp":287116726337,"id":49,"parentId":48,"tags":{},"startTime":1750489171203,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":29,"timestamp":287116726553,"id":51,"parentId":48,"tags":{},"startTime":1750489171203,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":89,"timestamp":287116726880,"id":52,"parentId":48,"tags":{},"startTime":1750489171203,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":34,"timestamp":287116727067,"id":53,"parentId":48,"tags":{},"startTime":1750489171203,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":35,"timestamp":287116727181,"id":54,"parentId":48,"tags":{},"startTime":1750489171204,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":805,"timestamp":287116726512,"id":50,"parentId":48,"tags":{},"startTime":1750489171203,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":1285,"timestamp":287116727940,"id":55,"parentId":48,"tags":{},"startTime":1750489171204,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":64,"timestamp":287116729422,"id":56,"parentId":48,"tags":{},"startTime":1750489171206,"traceId":"901c6e83d477b378"},{"name":"hash","duration":402,"timestamp":287116729673,"id":57,"parentId":48,"tags":{},"startTime":1750489171206,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":157,"timestamp":287116730070,"id":58,"parentId":48,"tags":{},"startTime":1750489171206,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":55,"timestamp":287116730185,"id":59,"parentId":48,"tags":{},"startTime":1750489171207,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":73,"timestamp":287116730256,"id":60,"parentId":48,"tags":{},"startTime":1750489171207,"traceId":"901c6e83d477b378"},{"name":"seal","duration":7346,"timestamp":287116726110,"id":48,"parentId":46,"tags":{},"startTime":1750489171203,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":24407,"timestamp":287116709250,"id":46,"parentId":3,"tags":{"name":"edge-server"},"startTime":1750489171186,"traceId":"901c6e83d477b378"},{"name":"emit","duration":35150,"timestamp":287116733858,"id":61,"parentId":3,"tags":{},"startTime":1750489171210,"traceId":"901c6e83d477b378"}]
[{"name":"make","duration":4759,"timestamp":287117313632,"id":66,"parentId":65,"tags":{},"startTime":1750489171790,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":60,"timestamp":287117318775,"id":68,"parentId":67,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":15,"timestamp":287117318885,"id":70,"parentId":67,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":23,"timestamp":287117318931,"id":71,"parentId":67,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":16,"timestamp":287117318989,"id":72,"parentId":67,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":15,"timestamp":287117319047,"id":73,"parentId":67,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":261,"timestamp":287117318867,"id":69,"parentId":67,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":32,"timestamp":287117319378,"id":74,"parentId":67,"tags":{},"startTime":1750489171796,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":20,"timestamp":287117319441,"id":75,"parentId":67,"tags":{},"startTime":1750489171796,"traceId":"901c6e83d477b378"},{"name":"hash","duration":187,"timestamp":287117320176,"id":76,"parentId":67,"tags":{},"startTime":1750489171797,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":70,"timestamp":287117320360,"id":77,"parentId":67,"tags":{},"startTime":1750489171797,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":24,"timestamp":287117320413,"id":78,"parentId":67,"tags":{},"startTime":1750489171797,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":45,"timestamp":287117320448,"id":79,"parentId":67,"tags":{},"startTime":1750489171797,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-generateClientManifest","duration":497,"timestamp":287117325550,"id":81,"parentId":65,"tags":{},"startTime":1750489171802,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":780,"timestamp":287117325303,"id":80,"parentId":65,"tags":{},"startTime":1750489171802,"traceId":"901c6e83d477b378"},{"name":"seal","duration":7958,"timestamp":287117318689,"id":67,"parentId":65,"tags":{},"startTime":1750489171795,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":22757,"timestamp":287117306883,"id":65,"parentId":62,"tags":{"name":"client"},"startTime":1750489171783,"traceId":"901c6e83d477b378"},{"name":"setup-dev-bundler","duration":4624457,"timestamp":287112920819,"id":2,"parentId":1,"tags":{},"startTime":1750489167397,"traceId":"901c6e83d477b378"},{"name":"emit","duration":223931,"timestamp":287117331901,"id":82,"parentId":62,"tags":{},"startTime":1750489171808,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-client","duration":295524,"timestamp":287117264585,"id":62,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489171741,"traceId":"901c6e83d477b378"},{"name":"make","duration":3168,"timestamp":287117577321,"id":84,"parentId":83,"tags":{},"startTime":1750489172054,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":170,"timestamp":287117582413,"id":86,"parentId":85,"tags":{},"startTime":1750489172059,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":26,"timestamp":287117583055,"id":88,"parentId":85,"tags":{},"startTime":1750489172059,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":424,"timestamp":287117583417,"id":89,"parentId":85,"tags":{},"startTime":1750489172060,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":28,"timestamp":287117583947,"id":90,"parentId":85,"tags":{},"startTime":1750489172060,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":20,"timestamp":287117584054,"id":91,"parentId":85,"tags":{},"startTime":1750489172060,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":1174,"timestamp":287117583001,"id":87,"parentId":85,"tags":{},"startTime":1750489172059,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":33,"timestamp":287117585151,"id":92,"parentId":85,"tags":{},"startTime":1750489172062,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":61,"timestamp":287117585239,"id":93,"parentId":85,"tags":{},"startTime":1750489172062,"traceId":"901c6e83d477b378"},{"name":"hash","duration":693,"timestamp":287117585480,"id":94,"parentId":85,"tags":{},"startTime":1750489172062,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":335,"timestamp":287117586167,"id":95,"parentId":85,"tags":{},"startTime":1750489172063,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":66,"timestamp":287117586457,"id":96,"parentId":85,"tags":{},"startTime":1750489172063,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":67,"timestamp":287117586539,"id":97,"parentId":85,"tags":{},"startTime":1750489172063,"traceId":"901c6e83d477b378"},{"name":"seal","duration":8867,"timestamp":287117581433,"id":85,"parentId":83,"tags":{},"startTime":1750489172058,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":17526,"timestamp":287117572925,"id":83,"parentId":63,"tags":{"name":"server"},"startTime":1750489172049,"traceId":"901c6e83d477b378"},{"name":"emit","duration":34585,"timestamp":287117590621,"id":98,"parentId":63,"tags":{},"startTime":1750489172067,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-server","duration":365514,"timestamp":287117265215,"id":63,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489171742,"traceId":"901c6e83d477b378"},{"name":"make","duration":824,"timestamp":287117641071,"id":100,"parentId":99,"tags":{},"startTime":1750489172117,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":81,"timestamp":287117644107,"id":102,"parentId":101,"tags":{},"startTime":1750489172121,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":21,"timestamp":287117644276,"id":104,"parentId":101,"tags":{},"startTime":1750489172121,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":42,"timestamp":287117644345,"id":105,"parentId":101,"tags":{},"startTime":1750489172121,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":27,"timestamp":287117644469,"id":106,"parentId":101,"tags":{},"startTime":1750489172121,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":3833,"timestamp":287117645225,"id":107,"parentId":101,"tags":{},"startTime":1750489172122,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":7096,"timestamp":287117644241,"id":103,"parentId":101,"tags":{},"startTime":1750489172121,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":37,"timestamp":287117651920,"id":108,"parentId":101,"tags":{},"startTime":1750489172128,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":29,"timestamp":287117652017,"id":109,"parentId":101,"tags":{},"startTime":1750489172128,"traceId":"901c6e83d477b378"},{"name":"hash","duration":446,"timestamp":287117652166,"id":110,"parentId":101,"tags":{},"startTime":1750489172129,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":95,"timestamp":287117652609,"id":111,"parentId":101,"tags":{},"startTime":1750489172129,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":31,"timestamp":287117652681,"id":112,"parentId":101,"tags":{},"startTime":1750489172129,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":70,"timestamp":287117652724,"id":113,"parentId":101,"tags":{},"startTime":1750489172129,"traceId":"901c6e83d477b378"},{"name":"seal","duration":12211,"timestamp":287117643957,"id":101,"parentId":99,"tags":{},"startTime":1750489172120,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":18215,"timestamp":287117638080,"id":99,"parentId":64,"tags":{"name":"edge-server"},"startTime":1750489172115,"traceId":"901c6e83d477b378"},{"name":"run-instrumentation-hook","duration":67,"timestamp":287118251244,"id":115,"parentId":1,"tags":{},"startTime":1750489172728,"traceId":"901c6e83d477b378"},{"name":"emit","duration":725649,"timestamp":287117656382,"id":114,"parentId":64,"tags":{},"startTime":1750489172133,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-edge-server","duration":1120307,"timestamp":287117265345,"id":64,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489171742,"traceId":"901c6e83d477b378"}]
[{"name":"start-dev-server","duration":8035921,"timestamp":287110374806,"id":1,"tags":{"cpus":"8","platform":"win32","memory.freeMem":"809373696","memory.totalMem":"8192450560","memory.heapSizeLimit":"4146069504","memory.rss":"184197120","memory.heapTotal":"123617280","memory.heapUsed":"91305424"},"startTime":1750489164852,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":1169041,"timestamp":287119991903,"id":123,"parentId":122,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750489174468,"traceId":"901c6e83d477b378"},{"name":"build-module-tsx","duration":148446,"timestamp":287121786581,"id":124,"parentId":123,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\dashboard\\page.tsx","layer":"rsc"},"startTime":1750489176263,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3445693,"timestamp":287118543806,"id":122,"parentId":121,"tags":{"request":"next-app-loader?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489173020,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":26259,"timestamp":287122094441,"id":132,"parentId":120,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"ssr"},"startTime":1750489176571,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":2278,"timestamp":287122120796,"id":133,"parentId":120,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"rsc"},"startTime":1750489176597,"traceId":"901c6e83d477b378"},{"name":"build-module-tsx","duration":245318,"timestamp":287122160567,"id":134,"parentId":132,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\dashboard\\page.tsx","layer":"ssr"},"startTime":1750489176637,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":155272,"timestamp":287122724361,"id":135,"parentId":134,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\api\\navigation.js","layer":"ssr"},"startTime":1750489177201,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":145099,"timestamp":287122777486,"id":137,"parentId":134,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\app-page\\vendored\\ssr\\react-jsx-dev-runtime.js","layer":"ssr"},"startTime":1750489177254,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":533849,"timestamp":287122773024,"id":136,"parentId":134,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next-auth\\react.js","layer":"ssr"},"startTime":1750489177249,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":658373,"timestamp":287122884607,"id":138,"parentId":134,"tags":{"name":"__barrel_optimize__?names=AlertCircle,BookOpen,Calendar,CheckCircle,Clock,FileText,LogOut,TrendingUp!=!C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\lucide-react.js","layer":"ssr"},"startTime":1750489177361,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":80259,"timestamp":287123651671,"id":139,"parentId":136,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next-auth\\lib\\client.js","layer":"ssr"},"startTime":1750489178128,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26104,"timestamp":287123710138,"id":143,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\circle-check-big.js","layer":"ssr"},"startTime":1750489178187,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":29000,"timestamp":287123708622,"id":140,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\circle-alert.js","layer":"ssr"},"startTime":1750489178185,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30010,"timestamp":287123709322,"id":141,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\calendar.js","layer":"ssr"},"startTime":1750489178186,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33527,"timestamp":287123709753,"id":142,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\book-open.js","layer":"ssr"},"startTime":1750489178186,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35107,"timestamp":287123712146,"id":145,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\clock.js","layer":"ssr"},"startTime":1750489178189,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37297,"timestamp":287123712693,"id":146,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\log-out.js","layer":"ssr"},"startTime":1750489178189,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":48429,"timestamp":287123710919,"id":144,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\file-text.js","layer":"ssr"},"startTime":1750489178187,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":50785,"timestamp":287123713047,"id":147,"parentId":138,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\trending-up.js","layer":"ssr"},"startTime":1750489178189,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":12579,"timestamp":287123774506,"id":148,"parentId":143,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\createLucideIcon.js","layer":"ssr"},"startTime":1750489178251,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":32823,"timestamp":287123809431,"id":149,"parentId":148,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\Icon.js","layer":"ssr"},"startTime":1750489178286,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":86374,"timestamp":287123815825,"id":150,"parentId":139,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@auth\\core\\errors.js","layer":"ssr"},"startTime":1750489178292,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":63404,"timestamp":287123853107,"id":151,"parentId":148,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\shared\\src\\utils.js","layer":"ssr"},"startTime":1750489178330,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":17184,"timestamp":287123902353,"id":152,"parentId":149,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\defaultAttributes.js","layer":"ssr"},"startTime":1750489178379,"traceId":"901c6e83d477b378"},{"name":"make","duration":5412436,"timestamp":287118525751,"id":121,"parentId":120,"tags":{},"startTime":1750489173002,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":106882,"timestamp":287124023623,"id":154,"parentId":153,"tags":{},"startTime":1750489178500,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":61,"timestamp":287124132427,"id":156,"parentId":153,"tags":{},"startTime":1750489178609,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":62034,"timestamp":287124132571,"id":157,"parentId":153,"tags":{},"startTime":1750489178609,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":219,"timestamp":287124197490,"id":158,"parentId":153,"tags":{},"startTime":1750489178674,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":126,"timestamp":287124199236,"id":159,"parentId":153,"tags":{},"startTime":1750489178676,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":76415,"timestamp":287124132332,"id":155,"parentId":153,"tags":{},"startTime":1750489178609,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":101367,"timestamp":287124264485,"id":160,"parentId":153,"tags":{},"startTime":1750489178741,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":129622,"timestamp":287124367031,"id":161,"parentId":153,"tags":{},"startTime":1750489178843,"traceId":"901c6e83d477b378"},{"name":"hash","duration":31406,"timestamp":287124523473,"id":162,"parentId":153,"tags":{},"startTime":1750489179000,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":1061,"timestamp":287124554867,"id":163,"parentId":153,"tags":{},"startTime":1750489179031,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":1267,"timestamp":287124555674,"id":164,"parentId":153,"tags":{},"startTime":1750489179032,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":489941,"timestamp":287124557013,"id":165,"parentId":153,"tags":{},"startTime":1750489179033,"traceId":"901c6e83d477b378"},{"name":"seal","duration":1497505,"timestamp":287124005819,"id":153,"parentId":120,"tags":{},"startTime":1750489178482,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":7260652,"timestamp":287118523893,"id":120,"parentId":118,"tags":{"name":"server"},"startTime":1750489173000,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-server","duration":7351500,"timestamp":287118517714,"id":118,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489172994,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":6658,"timestamp":287126648391,"id":173,"parentId":171,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750489181125,"traceId":"901c6e83d477b378"},{"name":"build-module-tsx","duration":456560,"timestamp":287126701340,"id":174,"parentId":173,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\dashboard\\page.tsx","layer":"app-pages-browser"},"startTime":1750489181178,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":1201567,"timestamp":287125966529,"id":168,"parentId":167,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489180443,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":112214,"timestamp":287128757565,"id":175,"parentId":174,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\api\\navigation.js","layer":"app-pages-browser"},"startTime":1750489183234,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":248822,"timestamp":287128765192,"id":176,"parentId":174,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next-auth\\react.js","layer":"app-pages-browser"},"startTime":1750489183242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":71290,"timestamp":287129226015,"id":177,"parentId":176,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next-auth\\lib\\client.js","layer":"app-pages-browser"},"startTime":1750489183702,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62106,"timestamp":287129407599,"id":179,"parentId":177,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@auth\\core\\errors.js","layer":"app-pages-browser"},"startTime":1750489183884,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":159852,"timestamp":287129357295,"id":178,"parentId":174,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react\\jsx-dev-runtime.js","layer":"app-pages-browser"},"startTime":1750489183834,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":82931,"timestamp":287129773934,"id":180,"parentId":174,"tags":{"name":"__barrel_optimize__?names=AlertCircle,BookOpen,Calendar,CheckCircle,Clock,FileText,LogOut,TrendingUp!=!C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\lucide-react.js","layer":"app-pages-browser"},"startTime":1750489184250,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3889277,"timestamp":287125967777,"id":170,"parentId":167,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489180444,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":31490,"timestamp":287129923571,"id":182,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\circle-alert.js","layer":"app-pages-browser"},"startTime":1750489184400,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3998118,"timestamp":287125968156,"id":172,"parentId":167,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489180445,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3999225,"timestamp":287125967100,"id":169,"parentId":167,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750489180444,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":47015,"timestamp":287129925918,"id":185,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\circle-check-big.js","layer":"app-pages-browser"},"startTime":1750489184402,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":50265,"timestamp":287129924053,"id":183,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\book-open.js","layer":"app-pages-browser"},"startTime":1750489184400,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":50491,"timestamp":287129925406,"id":184,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\calendar.js","layer":"app-pages-browser"},"startTime":1750489184402,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":54096,"timestamp":287129926277,"id":186,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\clock.js","layer":"app-pages-browser"},"startTime":1750489184403,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":218406,"timestamp":287129792966,"id":181,"parentId":178,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react\\cjs\\react-jsx-dev-runtime.development.js","layer":"app-pages-browser"},"startTime":1750489184269,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":93015,"timestamp":287129928438,"id":187,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\file-text.js","layer":"app-pages-browser"},"startTime":1750489184405,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":93394,"timestamp":287129929887,"id":189,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\trending-up.js","layer":"app-pages-browser"},"startTime":1750489184406,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":95192,"timestamp":287129929532,"id":188,"parentId":180,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\icons\\log-out.js","layer":"app-pages-browser"},"startTime":1750489184406,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":14443,"timestamp":287130033975,"id":190,"parentId":182,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\createLucideIcon.js","layer":"app-pages-browser"},"startTime":1750489184510,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":25047,"timestamp":287130058009,"id":191,"parentId":190,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\Icon.js","layer":"app-pages-browser"},"startTime":1750489184534,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":29817,"timestamp":287130058365,"id":192,"parentId":190,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\shared\\src\\utils.js","layer":"app-pages-browser"},"startTime":1750489184535,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":14815,"timestamp":287130100247,"id":193,"parentId":191,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\lucide-react\\dist\\esm\\defaultAttributes.js","layer":"app-pages-browser"},"startTime":1750489184577,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":4147228,"timestamp":287125968077,"id":171,"parentId":167,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489180445,"traceId":"901c6e83d477b378"},{"name":"make","duration":4226198,"timestamp":287125891583,"id":167,"parentId":166,"tags":{},"startTime":1750489180368,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":36472,"timestamp":287130145757,"id":195,"parentId":194,"tags":{},"startTime":1750489184622,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":57,"timestamp":287130182557,"id":197,"parentId":194,"tags":{},"startTime":1750489184659,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":629,"timestamp":287130182851,"id":198,"parentId":194,"tags":{},"startTime":1750489184659,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":446,"timestamp":287130183745,"id":199,"parentId":194,"tags":{},"startTime":1750489184660,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":617,"timestamp":287130184698,"id":200,"parentId":194,"tags":{},"startTime":1750489184661,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":6257,"timestamp":287130182445,"id":196,"parentId":194,"tags":{},"startTime":1750489184659,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":59351,"timestamp":287130225184,"id":201,"parentId":194,"tags":{},"startTime":1750489184702,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":68078,"timestamp":287130284817,"id":202,"parentId":194,"tags":{},"startTime":1750489184761,"traceId":"901c6e83d477b378"},{"name":"hash","duration":99345,"timestamp":287130406733,"id":203,"parentId":194,"tags":{},"startTime":1750489184883,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":1088,"timestamp":287130506048,"id":204,"parentId":194,"tags":{},"startTime":1750489184982,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":349,"timestamp":287130507010,"id":205,"parentId":194,"tags":{},"startTime":1750489184983,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":112694,"timestamp":287130507405,"id":206,"parentId":194,"tags":{},"startTime":1750489184984,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1572,"timestamp":287130636581,"id":208,"parentId":166,"tags":{},"startTime":1750489185113,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":6098,"timestamp":287130632125,"id":207,"parentId":166,"tags":{},"startTime":1750489185109,"traceId":"901c6e83d477b378"},{"name":"seal","duration":577572,"timestamp":287130140192,"id":194,"parentId":166,"tags":{},"startTime":1750489184617,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":4831889,"timestamp":287125886404,"id":166,"parentId":131,"tags":{"name":"client"},"startTime":1750489180363,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-client","duration":8717469,"timestamp":287122039200,"id":131,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489176516,"traceId":"901c6e83d477b378"}]
[{"name":"ensure-page","duration":74025,"timestamp":287130932928,"id":209,"parentId":3,"tags":{"inputPage":"/dashboard/page"},"startTime":1750489185409,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":477659,"timestamp":287131123625,"id":216,"parentId":215,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489185600,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":758516,"timestamp":287131124022,"id":220,"parentId":215,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489185600,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":827117,"timestamp":287131123980,"id":218,"parentId":215,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489185600,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":873493,"timestamp":287131124046,"id":221,"parentId":215,"tags":{"request":"next-client-pages-loader?absolutePagePath=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!"},"startTime":1750489185600,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":873654,"timestamp":287131123917,"id":217,"parentId":215,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750489185600,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":941293,"timestamp":287131124002,"id":219,"parentId":215,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489185600,"traceId":"901c6e83d477b378"},{"name":"make","duration":976335,"timestamp":287131089227,"id":215,"parentId":214,"tags":{},"startTime":1750489185566,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":9095,"timestamp":287132077554,"id":223,"parentId":222,"tags":{},"startTime":1750489186554,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":75,"timestamp":287132086964,"id":225,"parentId":222,"tags":{},"startTime":1750489186563,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":344,"timestamp":287132087306,"id":226,"parentId":222,"tags":{},"startTime":1750489186564,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":65,"timestamp":287132087784,"id":227,"parentId":222,"tags":{},"startTime":1750489186564,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":31,"timestamp":287132087926,"id":228,"parentId":222,"tags":{},"startTime":1750489186564,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":4585,"timestamp":287132086769,"id":224,"parentId":222,"tags":{},"startTime":1750489186563,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":3572,"timestamp":287132099901,"id":229,"parentId":222,"tags":{},"startTime":1750489186576,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":6960,"timestamp":287132103651,"id":230,"parentId":222,"tags":{},"startTime":1750489186580,"traceId":"901c6e83d477b378"},{"name":"hash","duration":19605,"timestamp":287132124835,"id":231,"parentId":222,"tags":{},"startTime":1750489186601,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":706,"timestamp":287132144426,"id":232,"parentId":222,"tags":{},"startTime":1750489186621,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":456,"timestamp":287132144957,"id":233,"parentId":222,"tags":{},"startTime":1750489186621,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":11226,"timestamp":287132145459,"id":234,"parentId":222,"tags":{},"startTime":1750489186622,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-generateClientManifest","duration":354,"timestamp":287132161260,"id":236,"parentId":214,"tags":{},"startTime":1750489186638,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":2349,"timestamp":287132159325,"id":235,"parentId":214,"tags":{},"startTime":1750489186637,"traceId":"901c6e83d477b378"},{"name":"seal","duration":128966,"timestamp":287132071751,"id":222,"parentId":214,"tags":{},"startTime":1750489186548,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":1132194,"timestamp":287131068718,"id":214,"parentId":211,"tags":{"name":"client"},"startTime":1750489185545,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-client","duration":1169722,"timestamp":287131035408,"id":211,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489185512,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":74120,"timestamp":287132249742,"id":240,"parentId":238,"tags":{"request":"next-app-loader?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489186726,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3090118,"timestamp":287132249437,"id":239,"parentId":238,"tags":{"request":"next-app-loader?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489186726,"traceId":"901c6e83d477b378"},{"name":"make","duration":3293271,"timestamp":287132219695,"id":238,"parentId":237,"tags":{},"startTime":1750489186696,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":12623,"timestamp":287135523803,"id":252,"parentId":251,"tags":{},"startTime":1750489190000,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":48,"timestamp":287135536584,"id":254,"parentId":251,"tags":{},"startTime":1750489190013,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":12626,"timestamp":287135536991,"id":255,"parentId":251,"tags":{},"startTime":1750489190013,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":43,"timestamp":287135549740,"id":256,"parentId":251,"tags":{},"startTime":1750489190026,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":51,"timestamp":287135549861,"id":257,"parentId":251,"tags":{},"startTime":1750489190026,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":17175,"timestamp":287135536529,"id":253,"parentId":251,"tags":{},"startTime":1750489190013,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":2275,"timestamp":287135560337,"id":258,"parentId":251,"tags":{},"startTime":1750489190037,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":5717,"timestamp":287135562712,"id":259,"parentId":251,"tags":{},"startTime":1750489190039,"traceId":"901c6e83d477b378"},{"name":"hash","duration":9142,"timestamp":287135576010,"id":260,"parentId":251,"tags":{},"startTime":1750489190052,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":450,"timestamp":287135585145,"id":261,"parentId":251,"tags":{},"startTime":1750489190062,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":519,"timestamp":287135585479,"id":262,"parentId":251,"tags":{},"startTime":1750489190062,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":5259,"timestamp":287135586113,"id":263,"parentId":251,"tags":{},"startTime":1750489190063,"traceId":"901c6e83d477b378"},{"name":"seal","duration":132549,"timestamp":287135519980,"id":251,"parentId":237,"tags":{},"startTime":1750489189996,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":3451596,"timestamp":287132217287,"id":237,"parentId":213,"tags":{"name":"server"},"startTime":1750489186694,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-server","duration":4650760,"timestamp":287131036316,"id":213,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489185513,"traceId":"901c6e83d477b378"}]
[{"name":"ensure-page","duration":4680207,"timestamp":287131013325,"id":210,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1750489185490,"traceId":"901c6e83d477b378"},{"name":"next-client-pages-loader","duration":908,"timestamp":287135975174,"id":275,"parentId":274,"tags":{"absolutePagePath":"next/dist/pages/_app"},"startTime":1750489190452,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":16678,"timestamp":287135963488,"id":274,"parentId":271,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!","layer":"pages-dir-browser"},"startTime":1750489190440,"traceId":"901c6e83d477b378"},{"name":"next-client-pages-loader","duration":105,"timestamp":287135981499,"id":277,"parentId":276,"tags":{"absolutePagePath":"next/dist/pages/_error"},"startTime":1750489190458,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":1936,"timestamp":287135980491,"id":276,"parentId":273,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-client-pages-loader.js?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!","layer":"pages-dir-browser"},"startTime":1750489190457,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30491,"timestamp":287135991585,"id":281,"parentId":270,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-next-dev.js","layer":"app-pages-browser"},"startTime":1750489190468,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":38881,"timestamp":287135991455,"id":280,"parentId":268,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\next-dev.js","layer":"pages-dir-browser"},"startTime":1750489190468,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":48183,"timestamp":287135993082,"id":282,"parentId":267,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\amp-dev.js","layer":"pages-dir-browser"},"startTime":1750489190470,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62066,"timestamp":287135982485,"id":278,"parentId":266,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js","layer":"pages-dir-browser"},"startTime":1750489190459,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":53872,"timestamp":287135993674,"id":283,"parentId":269,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js","layer":"app-pages-browser"},"startTime":1750489190470,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":109060,"timestamp":287135991130,"id":279,"parentId":272,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\router.js","layer":"pages-dir-browser"},"startTime":1750489190468,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30955,"timestamp":287136124915,"id":284,"parentId":274,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_app.js","layer":"pages-dir-browser"},"startTime":1750489190601,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":57000,"timestamp":287136125827,"id":285,"parentId":276,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_error.js","layer":"pages-dir-browser"},"startTime":1750489190602,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":45325,"timestamp":287136158100,"id":287,"parentId":281,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-webpack.js","layer":"app-pages-browser"},"startTime":1750489190635,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":63597,"timestamp":287136158407,"id":288,"parentId":281,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-bootstrap.js","layer":"app-pages-browser"},"startTime":1750489190635,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":70033,"timestamp":287136158616,"id":289,"parentId":280,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\page-bootstrap.js","layer":"pages-dir-browser"},"startTime":1750489190635,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":89967,"timestamp":287136157478,"id":286,"parentId":281,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-index.js","layer":"app-pages-browser"},"startTime":1750489190634,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":109763,"timestamp":287136158787,"id":290,"parentId":280,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\hot-middleware-client.js","layer":"pages-dir-browser"},"startTime":1750489190635,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":113631,"timestamp":287136159563,"id":292,"parentId":282,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\fouc.js","layer":"pages-dir-browser"},"startTime":1750489190636,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":117631,"timestamp":287136159731,"id":293,"parentId":282,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\on-demand-entries-client.js","layer":"pages-dir-browser"},"startTime":1750489190636,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":124356,"timestamp":287136159249,"id":291,"parentId":280,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\webpack.js","layer":"pages-dir-browser"},"startTime":1750489190636,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":159378,"timestamp":287136187772,"id":295,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\with-router.js","layer":"pages-dir-browser"},"startTime":1750489190664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":221052,"timestamp":287136187379,"id":294,"parentId":280,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\index.js","layer":"pages-dir-browser"},"startTime":1750489190664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":133408,"timestamp":287136291513,"id":296,"parentId":281,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\initialize-for-app-router.js","layer":"app-pages-browser"},"startTime":1750489190768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":61989,"timestamp":287136491627,"id":304,"parentId":282,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\websocket.js","layer":"pages-dir-browser"},"startTime":1750489190968,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":265475,"timestamp":287136293171,"id":298,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-error.js","layer":"pages-dir-browser"},"startTime":1750489190770,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":71330,"timestamp":287136490915,"id":301,"parentId":289,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\require-instrumentation-client.js","layer":"pages-dir-browser"},"startTime":1750489190967,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":275885,"timestamp":287136292095,"id":297,"parentId":281,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\require-instrumentation-client.js","layer":"app-pages-browser"},"startTime":1750489190769,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":286384,"timestamp":287136293577,"id":299,"parentId":278,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\internal\\helpers.js","layer":"pages-dir-browser"},"startTime":1750489190770,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":294525,"timestamp":287136293909,"id":300,"parentId":283,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\internal\\helpers.js","layer":"app-pages-browser"},"startTime":1750489190770,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":120334,"timestamp":287136491310,"id":303,"parentId":282,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\shared.js","layer":"pages-dir-browser"},"startTime":1750489190968,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":167462,"timestamp":287136535713,"id":305,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489191012,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":175161,"timestamp":287136538827,"id":307,"parentId":284,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils.js","layer":"pages-dir-browser"},"startTime":1750489191015,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":256348,"timestamp":287136538006,"id":306,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\router.js","layer":"pages-dir-browser"},"startTime":1750489191014,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":159780,"timestamp":287136646969,"id":314,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-call-server.js","layer":"app-pages-browser"},"startTime":1750489191123,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":166673,"timestamp":287136647173,"id":315,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-build-id.js","layer":"app-pages-browser"},"startTime":1750489191124,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":169993,"timestamp":287136646618,"id":313,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-find-source-map-url.js","layer":"app-pages-browser"},"startTime":1750489191123,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":194200,"timestamp":287136644122,"id":308,"parentId":285,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head.js","layer":"pages-dir-browser"},"startTime":1750489191121,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":194092,"timestamp":287136647514,"id":316,"parentId":287,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\encode-uri-path.js","layer":"app-pages-browser"},"startTime":1750489191124,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":199722,"timestamp":287136647910,"id":317,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\app-link-gc.js","layer":"app-pages-browser"},"startTime":1750489191124,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":202073,"timestamp":287136648309,"id":319,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head-manager-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489191125,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":212091,"timestamp":287136648762,"id":321,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\app-router-instance.js","layer":"app-pages-browser"},"startTime":1750489191125,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":246989,"timestamp":287136648529,"id":320,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\app-router.js","layer":"app-pages-browser"},"startTime":1750489191125,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":250215,"timestamp":287136648934,"id":322,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\app-router-context.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750489191125,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":254172,"timestamp":287136648126,"id":318,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head-manager-context.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750489191125,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":256453,"timestamp":287136649101,"id":323,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\app-router-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489191126,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":260275,"timestamp":287136649238,"id":324,"parentId":289,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\initialize-for-page-router.js","layer":"pages-dir-browser"},"startTime":1750489191126,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":274130,"timestamp":287136649399,"id":325,"parentId":289,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\hot-reloader-client.js","layer":"pages-dir-browser"},"startTime":1750489191126,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":468425,"timestamp":287136491134,"id":302,"parentId":282,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\dev\\hot-reloader-types.js","layer":"pages-dir-browser"},"startTime":1750489190968,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":362648,"timestamp":287136646375,"id":312,"parentId":291,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\deployment-id.js","layer":"pages-dir-browser"},"startTime":1750489191123,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":365033,"timestamp":287136645718,"id":311,"parentId":287,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\deployment-id.js","layer":"app-pages-browser"},"startTime":1750489191122,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":367453,"timestamp":287136644588,"id":309,"parentId":278,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-refresh\\runtime.js","layer":"pages-dir-browser"},"startTime":1750489191121,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":368172,"timestamp":287136645162,"id":310,"parentId":283,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-refresh\\runtime.js","layer":"app-pages-browser"},"startTime":1750489191122,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":46896,"timestamp":287137079295,"id":327,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\remove-base-path.js","layer":"pages-dir-browser"},"startTime":1750489191556,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":52276,"timestamp":287137079742,"id":328,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\route-announcer.js","layer":"pages-dir-browser"},"startTime":1750489191556,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":1985,"timestamp":287137139575,"id":357,"parentId":306,"tags":{"layer":null},"startTime":1750489191616,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83335,"timestamp":287137078274,"id":326,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\head-manager.js","layer":"pages-dir-browser"},"startTime":1750489191555,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":90512,"timestamp":287137080460,"id":329,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\page-loader.js","layer":"pages-dir-browser"},"startTime":1750489191557,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":96479,"timestamp":287137080806,"id":330,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\runtime-config.external.js","layer":"pages-dir-browser"},"startTime":1750489191557,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":100578,"timestamp":287137080972,"id":331,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\has-base-path.js","layer":"pages-dir-browser"},"startTime":1750489191557,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":103756,"timestamp":287137081100,"id":332,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\mitt.js","layer":"pages-dir-browser"},"startTime":1750489191558,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":116473,"timestamp":287137082260,"id":333,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\script.js","layer":"pages-dir-browser"},"startTime":1750489191559,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":125417,"timestamp":287137082608,"id":334,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\adapters.js","layer":"pages-dir-browser"},"startTime":1750489191559,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":133552,"timestamp":287137083048,"id":336,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\hooks-client-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489191559,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":139110,"timestamp":287137082870,"id":335,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\image-config-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489191559,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":95413,"timestamp":287137133941,"id":337,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\is-next-router-error.js","layer":"pages-dir-browser"},"startTime":1750489191610,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":118904,"timestamp":287137135208,"id":340,"parentId":289,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\dev-build-indicator.js","layer":"pages-dir-browser"},"startTime":1750489191612,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":130026,"timestamp":287137134418,"id":338,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\pages-dev-overlay.js","layer":"pages-dir-browser"},"startTime":1750489191611,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":133299,"timestamp":287137135529,"id":341,"parentId":296,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\dev-build-indicator.js","layer":"app-pages-browser"},"startTime":1750489191612,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":150754,"timestamp":287137134894,"id":339,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\client.js","layer":"pages-dir-browser"},"startTime":1750489191611,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":154631,"timestamp":287137135892,"id":343,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-dynamic.js","layer":"pages-dir-browser"},"startTime":1750489191612,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":181150,"timestamp":287137135989,"id":344,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\handle-smooth-scroll.js","layer":"pages-dir-browser"},"startTime":1750489191612,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":189150,"timestamp":287137136732,"id":347,"parentId":289,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\runtime-error-handler.js","layer":"pages-dir-browser"},"startTime":1750489191613,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":192019,"timestamp":287137136993,"id":348,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\globals\\handle-global-errors.js","layer":"app-pages-browser"},"startTime":1750489191613,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":199275,"timestamp":287137135749,"id":342,"parentId":289,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\querystring.js","layer":"pages-dir-browser"},"startTime":1750489191612,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":201348,"timestamp":287137137252,"id":349,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\globals\\patch-console.js","layer":"app-pages-browser"},"startTime":1750489191614,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":205874,"timestamp":287137137955,"id":351,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\on-recoverable-error.js","layer":"app-pages-browser"},"startTime":1750489191614,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":211663,"timestamp":287137137497,"id":350,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\error-boundary-callbacks.js","layer":"app-pages-browser"},"startTime":1750489191614,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":217300,"timestamp":287137138776,"id":353,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\create-initial-router-state.js","layer":"app-pages-browser"},"startTime":1750489191615,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":221961,"timestamp":287137138601,"id":352,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\on-recoverable-error.js","layer":"pages-dir-browser"},"startTime":1750489191615,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":222361,"timestamp":287137141665,"id":358,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\hooks-client-context.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750489191618,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":244879,"timestamp":287137139235,"id":356,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\tracing\\report-to-socket.js","layer":"pages-dir-browser"},"startTime":1750489191616,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":249873,"timestamp":287137138921,"id":354,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\app\\client-entry.js","layer":"app-pages-browser"},"startTime":1750489191615,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":262065,"timestamp":287137139067,"id":355,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\tracing\\tracer.js","layer":"pages-dir-browser"},"startTime":1750489191615,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":522327,"timestamp":287137136522,"id":346,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\polyfills\\polyfill-module.js","layer":"pages-dir-browser"},"startTime":1750489191613,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":527061,"timestamp":287137136267,"id":345,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\polyfills\\polyfill-module.js","layer":"app-pages-browser"},"startTime":1750489191613,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34137,"timestamp":287137725900,"id":359,"parentId":298,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-plain-object.js","layer":"pages-dir-browser"},"startTime":1750489192202,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83369,"timestamp":287137763961,"id":364,"parentId":314,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\use-action-queue.js","layer":"app-pages-browser"},"startTime":1750489192240,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":89352,"timestamp":287137763355,"id":363,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\remove-locale.js","layer":"pages-dir-browser"},"startTime":1750489192240,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":91565,"timestamp":287137764859,"id":368,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\add-base-path.js","layer":"pages-dir-browser"},"startTime":1750489192241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":94972,"timestamp":287137765021,"id":369,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\add-base-path.js","layer":"app-pages-browser"},"startTime":1750489192241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":110164,"timestamp":287137764359,"id":365,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\detect-domain-locale.js","layer":"pages-dir-browser"},"startTime":1750489192241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":137470,"timestamp":287137764547,"id":366,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\route-loader.js","layer":"pages-dir-browser"},"startTime":1750489192241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":159446,"timestamp":287137765261,"id":371,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\add-locale.js","layer":"pages-dir-browser"},"startTime":1750489192242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":168577,"timestamp":287137765152,"id":370,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\resolve-href.js","layer":"pages-dir-browser"},"startTime":1750489192242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":170315,"timestamp":287137766215,"id":375,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\remove-trailing-slash.js","layer":"pages-dir-browser"},"startTime":1750489192243,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":174063,"timestamp":287137765929,"id":374,"parentId":314,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\router-reducer-types.js","layer":"app-pages-browser"},"startTime":1750489192242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":178072,"timestamp":287137766380,"id":376,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\parse-relative-url.js","layer":"pages-dir-browser"},"startTime":1750489192243,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":182509,"timestamp":287137766570,"id":377,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\route-matcher.js","layer":"pages-dir-browser"},"startTime":1750489192243,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":185205,"timestamp":287137766883,"id":379,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\parse-path.js","layer":"pages-dir-browser"},"startTime":1750489192243,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":188416,"timestamp":287137767106,"id":380,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\get-next-pathname-info.js","layer":"pages-dir-browser"},"startTime":1750489192244,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":203709,"timestamp":287137766748,"id":378,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\route-regex.js","layer":"pages-dir-browser"},"startTime":1750489192243,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":214026,"timestamp":287137765558,"id":373,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\bloom-filter.js","layer":"pages-dir-browser"},"startTime":1750489192242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":217166,"timestamp":287137767873,"id":381,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\format-url.js","layer":"pages-dir-browser"},"startTime":1750489192244,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":221239,"timestamp":287137769121,"id":382,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\format-next-pathname-info.js","layer":"pages-dir-browser"},"startTime":1750489192246,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":225817,"timestamp":287137769800,"id":385,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\omit.js","layer":"pages-dir-browser"},"startTime":1750489192246,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":230347,"timestamp":287137769451,"id":383,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-bot.js","layer":"pages-dir-browser"},"startTime":1750489192246,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":243525,"timestamp":287137769945,"id":386,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\compare-states.js","layer":"pages-dir-browser"},"startTime":1750489192246,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":246318,"timestamp":287137770088,"id":387,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-local-url.js","layer":"pages-dir-browser"},"startTime":1750489192247,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":298083,"timestamp":287137802249,"id":388,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interpolate-as.js","layer":"pages-dir-browser"},"startTime":1750489192279,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":333691,"timestamp":287137769621,"id":384,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-bot.js","layer":"app-pages-browser"},"startTime":1750489192246,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":301172,"timestamp":287137805246,"id":390,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\has-base-path.js","layer":"app-pages-browser"},"startTime":1750489192282,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":308605,"timestamp":287137804208,"id":389,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\remove-base-path.js","layer":"app-pages-browser"},"startTime":1750489192281,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":310796,"timestamp":287137805722,"id":391,"parentId":304,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\get-socket-url.js","layer":"pages-dir-browser"},"startTime":1750489192282,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":312605,"timestamp":287137807857,"id":394,"parentId":308,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\side-effect.js","layer":"pages-dir-browser"},"startTime":1750489192284,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":322977,"timestamp":287137807566,"id":393,"parentId":308,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\amp-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489192284,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":327550,"timestamp":287137806593,"id":392,"parentId":308,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\amp-mode.js","layer":"pages-dir-browser"},"startTime":1750489192283,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":414471,"timestamp":287137726591,"id":360,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react\\jsx-runtime.js","layer":"app-pages-browser"},"startTime":1750489192203,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":115896,"timestamp":287138034371,"id":395,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-thenable.js","layer":"app-pages-browser"},"startTime":1750489192511,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":120024,"timestamp":287138035722,"id":397,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\router-reducer.js","layer":"app-pages-browser"},"startTime":1750489192512,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":128587,"timestamp":287138035947,"id":398,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\segment-cache.js","layer":"app-pages-browser"},"startTime":1750489192512,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":133642,"timestamp":287138036133,"id":399,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\create-href-from-url.js","layer":"app-pages-browser"},"startTime":1750489192513,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":135723,"timestamp":287138036990,"id":402,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\denormalize-page-path.js","layer":"pages-dir-browser"},"startTime":1750489192513,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":140479,"timestamp":287138036616,"id":401,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\i18n\\normalize-locale-path.js","layer":"pages-dir-browser"},"startTime":1750489192513,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":146558,"timestamp":287138037176,"id":403,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-boundary.js","layer":"app-pages-browser"},"startTime":1750489192514,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":151731,"timestamp":287138037377,"id":404,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\app-router-announcer.js","layer":"app-pages-browser"},"startTime":1750489192514,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":164576,"timestamp":287138035266,"id":396,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\links.js","layer":"app-pages-browser"},"startTime":1750489192512,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":163118,"timestamp":287138039084,"id":406,"parentId":308,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils\\warn-once.js","layer":"pages-dir-browser"},"startTime":1750489192516,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":166117,"timestamp":287138039305,"id":407,"parentId":321,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\prefetch-reducer.js","layer":"app-pages-browser"},"startTime":1750489192516,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":184937,"timestamp":287138038747,"id":405,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\error-boundary.js","layer":"app-pages-browser"},"startTime":1750489192515,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39660,"timestamp":287138253808,"id":418,"parentId":351,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-error.js","layer":"app-pages-browser"},"startTime":1750489192730,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":607690,"timestamp":287137764675,"id":367,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-api-route.js","layer":"pages-dir-browser"},"startTime":1750489192241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":610497,"timestamp":287137762882,"id":362,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-server-dom-webpack\\client.js","layer":"app-pages-browser"},"startTime":1750489192239,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":615785,"timestamp":287137761872,"id":361,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-dom\\client.js","layer":"app-pages-browser"},"startTime":1750489192238,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":622473,"timestamp":287137765372,"id":372,"parentId":306,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\constants.js","layer":"pages-dir-browser"},"startTime":1750489192242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":149457,"timestamp":287138252111,"id":411,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\compute-changed-path.js","layer":"app-pages-browser"},"startTime":1750489192729,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":152991,"timestamp":287138251118,"id":408,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\unresolved-thenable.js","layer":"app-pages-browser"},"startTime":1750489192728,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":161108,"timestamp":287138251855,"id":410,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect.js","layer":"app-pages-browser"},"startTime":1750489192728,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":167442,"timestamp":287138251600,"id":409,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\nav-failure-handler.js","layer":"app-pages-browser"},"startTime":1750489192728,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":171315,"timestamp":287138252677,"id":413,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\dev-root-http-access-fallback-boundary.js","layer":"app-pages-browser"},"startTime":1750489192729,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":174534,"timestamp":287138253149,"id":415,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\find-head-in-cache.js","layer":"app-pages-browser"},"startTime":1750489192730,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":184715,"timestamp":287138252270,"id":412,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-error.js","layer":"app-pages-browser"},"startTime":1750489192729,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":191390,"timestamp":287138254168,"id":419,"parentId":337,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-error.js","layer":"pages-dir-browser"},"startTime":1750489192731,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":42807,"timestamp":287138477685,"id":453,"parentId":354,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\dev\\hot-reloader-types.js","layer":"app-pages-browser"},"startTime":1750489192954,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":321817,"timestamp":287138252974,"id":414,"parentId":320,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\app\\hot-reloader-client.js","layer":"app-pages-browser"},"startTime":1750489192729,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":122501,"timestamp":287138468714,"id":421,"parentId":324,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\handle-dev-build-indicator-hmr-events.js","layer":"pages-dir-browser"},"startTime":1750489192945,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":135097,"timestamp":287138466666,"id":420,"parentId":325,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\format-webpack-messages.js","layer":"pages-dir-browser"},"startTime":1750489192943,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":137055,"timestamp":287138470098,"id":424,"parentId":350,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\is-next-router-error.js","layer":"app-pages-browser"},"startTime":1750489192947,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":152576,"timestamp":287138469943,"id":423,"parentId":325,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\turbopack-hot-reloader-common.js","layer":"pages-dir-browser"},"startTime":1750489192946,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":156871,"timestamp":287138469698,"id":422,"parentId":325,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\report-hmr-latency.js","layer":"pages-dir-browser"},"startTime":1750489192946,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":158843,"timestamp":287138471286,"id":429,"parentId":329,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\get-asset-path-from-route.js","layer":"pages-dir-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":162170,"timestamp":287138470885,"id":427,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\portal\\index.js","layer":"pages-dir-browser"},"startTime":1750489192947,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":165580,"timestamp":287138471626,"id":432,"parentId":333,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\request-idle-callback.js","layer":"pages-dir-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":168417,"timestamp":287138471517,"id":431,"parentId":326,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\set-attributes-from-props.js","layer":"pages-dir-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":171234,"timestamp":287138471396,"id":430,"parentId":331,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\path-has-prefix.js","layer":"pages-dir-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":174605,"timestamp":287138471730,"id":433,"parentId":335,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\image-config.js","layer":"pages-dir-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":177360,"timestamp":287138471827,"id":434,"parentId":351,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\report-global-error.js","layer":"app-pages-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":186148,"timestamp":287138471157,"id":428,"parentId":329,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\constants.js","layer":"pages-dir-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":194325,"timestamp":287138472201,"id":437,"parentId":349,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\globals\\intercept-console-error.js","layer":"app-pages-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":243898,"timestamp":287138472078,"id":436,"parentId":350,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\use-error-handler.js","layer":"app-pages-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":264955,"timestamp":287138472325,"id":438,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\hydration-error-info.js","layer":"pages-dir-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":267166,"timestamp":287138472439,"id":439,"parentId":334,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\as-path-to-search-params.js","layer":"pages-dir-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":282286,"timestamp":287138472537,"id":440,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\attach-hydration-error-state.js","layer":"pages-dir-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":296341,"timestamp":287138471954,"id":435,"parentId":351,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\stitched-error.js","layer":"app-pages-browser"},"startTime":1750489192948,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":300010,"timestamp":287138472648,"id":441,"parentId":340,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\initialize.js","layer":"pages-dir-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":307432,"timestamp":287138472815,"id":442,"parentId":341,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\initialize.js","layer":"app-pages-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":323718,"timestamp":287138473571,"id":445,"parentId":338,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\hooks.js","layer":"pages-dir-browser"},"startTime":1750489192950,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":338733,"timestamp":287138473943,"id":446,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\bus.js","layer":"pages-dir-browser"},"startTime":1750489192950,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":348337,"timestamp":287138473049,"id":443,"parentId":338,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\pages-dev-overlay-error-boundary.js","layer":"pages-dir-browser"},"startTime":1750489192949,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":361256,"timestamp":287138473344,"id":444,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\parse-stack.js","layer":"pages-dir-browser"},"startTime":1750489192950,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":365551,"timestamp":287138474168,"id":447,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\node-stack-frames.js","layer":"pages-dir-browser"},"startTime":1750489192951,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":372951,"timestamp":287138474483,"id":449,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\parse-component-stack.js","layer":"pages-dir-browser"},"startTime":1750489192951,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":377935,"timestamp":287138474358,"id":448,"parentId":339,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\get-error-by-type.js","layer":"pages-dir-browser"},"startTime":1750489192951,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":379837,"timestamp":287138474884,"id":451,"parentId":352,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\report-global-error.js","layer":"pages-dir-browser"},"startTime":1750489192951,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":383128,"timestamp":287138479527,"id":454,"parentId":354,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\get-socket-url.js","layer":"app-pages-browser"},"startTime":1750489192956,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":390085,"timestamp":287138476585,"id":452,"parentId":352,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\stitched-error.js","layer":"pages-dir-browser"},"startTime":1750489192953,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":389457,"timestamp":287138480922,"id":458,"parentId":352,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\bailout-to-csr.js","layer":"pages-dir-browser"},"startTime":1750489192957,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":392288,"timestamp":287138480672,"id":457,"parentId":351,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\bailout-to-csr.js","layer":"app-pages-browser"},"startTime":1750489192957,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":398439,"timestamp":287138480418,"id":456,"parentId":337,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\http-access-fallback.js","layer":"pages-dir-browser"},"startTime":1750489192957,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":409585,"timestamp":287138474598,"id":450,"parentId":343,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interception-routes.js","layer":"pages-dir-browser"},"startTime":1750489192951,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":405620,"timestamp":287138481089,"id":459,"parentId":338,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\font\\font-styles.js","layer":"pages-dir-browser"},"startTime":1750489192958,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":408558,"timestamp":287138481211,"id":460,"parentId":338,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\dev-overlay.js","layer":"pages-dir-browser"},"startTime":1750489192958,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":421081,"timestamp":287138481319,"id":461,"parentId":353,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\flight-data-helpers.js","layer":"app-pages-browser"},"startTime":1750489192958,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":442451,"timestamp":287138484308,"id":464,"parentId":353,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\refetch-inactive-parallel-segments.js","layer":"app-pages-browser"},"startTime":1750489192961,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":448632,"timestamp":287138484019,"id":463,"parentId":353,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\fill-lazy-items-till-leaf-with-head.js","layer":"app-pages-browser"},"startTime":1750489192960,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":453521,"timestamp":287138483326,"id":462,"parentId":350,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\app\\app-dev-overlay-error-boundary.js","layer":"app-pages-browser"},"startTime":1750489192960,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":903799,"timestamp":287138036281,"id":400,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react\\index.js","layer":"app-pages-browser"},"startTime":1750489192513,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":879625,"timestamp":287138253325,"id":416,"parentId":284,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\jsx-runtime.js","layer":"pages-dir-browser"},"startTime":1750489192730,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":885135,"timestamp":287138253606,"id":417,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\client.js","layer":"pages-dir-browser"},"startTime":1750489192730,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":785541,"timestamp":287138480141,"id":455,"parentId":294,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-is\\index.js","layer":"pages-dir-browser"},"startTime":1750489192957,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":804159,"timestamp":287138470262,"id":425,"parentId":309,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-refresh\\cjs\\react-refresh-runtime.development.js","layer":"pages-dir-browser"},"startTime":1750489192947,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":842743,"timestamp":287138470425,"id":426,"parentId":310,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-refresh\\cjs\\react-refresh-runtime.development.js","layer":"app-pages-browser"},"startTime":1750489192947,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":119359,"timestamp":287139207130,"id":467,"parentId":366,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\encode-uri-path.js","layer":"pages-dir-browser"},"startTime":1750489193684,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":121884,"timestamp":287139207691,"id":468,"parentId":390,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\path-has-prefix.js","layer":"app-pages-browser"},"startTime":1750489193684,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":120270,"timestamp":287139220312,"id":473,"parentId":418,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-plain-object.js","layer":"app-pages-browser"},"startTime":1750489193697,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":129090,"timestamp":287139221166,"id":474,"parentId":411,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interception-routes.js","layer":"app-pages-browser"},"startTime":1750489193698,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":132627,"timestamp":287139221827,"id":476,"parentId":369,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\normalize-trailing-slash.js","layer":"app-pages-browser"},"startTime":1750489193698,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":138428,"timestamp":287139221479,"id":475,"parentId":368,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\normalize-trailing-slash.js","layer":"pages-dir-browser"},"startTime":1750489193698,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3440278,"timestamp":287135920073,"id":266,"parentId":265,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3439947,"timestamp":287135920436,"id":269,"parentId":265,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":67,"timestamp":287139364208,"id":506,"parentId":301,"tags":{"layer":null},"startTime":1750489193841,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":163889,"timestamp":287139222309,"id":478,"parentId":369,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-path-prefix.js","layer":"app-pages-browser"},"startTime":1750489193699,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":170377,"timestamp":287139222116,"id":477,"parentId":368,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-path-prefix.js","layer":"pages-dir-browser"},"startTime":1750489193699,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":175585,"timestamp":287139222568,"id":479,"parentId":378,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\escape-regexp.js","layer":"pages-dir-browser"},"startTime":1750489193699,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":201445,"timestamp":287139205252,"id":465,"parentId":353,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\prefetch-cache-utils.js","layer":"app-pages-browser"},"startTime":1750489193682,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":186784,"timestamp":287139222877,"id":481,"parentId":380,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\remove-path-prefix.js","layer":"pages-dir-browser"},"startTime":1750489193699,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":189200,"timestamp":287139222991,"id":482,"parentId":382,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-path-suffix.js","layer":"pages-dir-browser"},"startTime":1750489193699,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":192086,"timestamp":287139222759,"id":480,"parentId":382,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-locale.js","layer":"pages-dir-browser"},"startTime":1750489193699,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":194464,"timestamp":287139223404,"id":485,"parentId":366,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\trusted-types.js","layer":"pages-dir-browser"},"startTime":1750489193700,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":197949,"timestamp":287139223114,"id":483,"parentId":383,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\html-bots.js","layer":"pages-dir-browser"},"startTime":1750489193700,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":200552,"timestamp":287139223243,"id":484,"parentId":384,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\html-bots.js","layer":"app-pages-browser"},"startTime":1750489193700,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":204322,"timestamp":287139223548,"id":486,"parentId":424,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\http-access-fallback.js","layer":"app-pages-browser"},"startTime":1750489193700,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":206279,"timestamp":287139224064,"id":488,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\runtime-error-handler.js","layer":"app-pages-browser"},"startTime":1750489193700,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":213428,"timestamp":287139224272,"id":489,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\format-webpack-messages.js","layer":"app-pages-browser"},"startTime":1750489193701,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":216382,"timestamp":287139224679,"id":492,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\handle-dev-build-indicator-hmr-events.js","layer":"app-pages-browser"},"startTime":1750489193701,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":220250,"timestamp":287139224556,"id":491,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\parse-component-stack.js","layer":"app-pages-browser"},"startTime":1750489193701,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":226682,"timestamp":287139223695,"id":487,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\shared.js","layer":"app-pages-browser"},"startTime":1750489193700,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":230737,"timestamp":287139224420,"id":490,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\parse-stack.js","layer":"app-pages-browser"},"startTime":1750489193701,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":236126,"timestamp":287139224830,"id":493,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\report-hmr-latency.js","layer":"app-pages-browser"},"startTime":1750489193701,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":239487,"timestamp":287139225858,"id":497,"parentId":391,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\normalized-asset-prefix.js","layer":"pages-dir-browser"},"startTime":1750489193702,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":244448,"timestamp":287139225281,"id":496,"parentId":405,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\navigation-untracked.js","layer":"app-pages-browser"},"startTime":1750489193702,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":246010,"timestamp":287139226122,"id":498,"parentId":402,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\normalize-path-sep.js","layer":"pages-dir-browser"},"startTime":1750489193703,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":252277,"timestamp":287139226278,"id":499,"parentId":407,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\promise-queue.js","layer":"app-pages-browser"},"startTime":1750489193703,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":258742,"timestamp":287139224967,"id":494,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\turbopack-hot-reloader-common.js","layer":"app-pages-browser"},"startTime":1750489193701,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":264325,"timestamp":287139225111,"id":495,"parentId":403,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\navigation.js","layer":"app-pages-browser"},"startTime":1750489193702,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":160693,"timestamp":287139362573,"id":500,"parentId":397,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\server-patch-reducer.js","layer":"app-pages-browser"},"startTime":1750489193839,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":165519,"timestamp":287139363550,"id":503,"parentId":397,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\hmr-refresh-reducer.js","layer":"app-pages-browser"},"startTime":1750489193840,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":168707,"timestamp":287139363310,"id":502,"parentId":397,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\restore-reducer.js","layer":"app-pages-browser"},"startTime":1750489193840,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":215634,"timestamp":287139363789,"id":504,"parentId":397,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\refresh-reducer.js","layer":"app-pages-browser"},"startTime":1750489193840,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":218460,"timestamp":287139364626,"id":508,"parentId":436,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\attach-hydration-error-state.js","layer":"app-pages-browser"},"startTime":1750489193841,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":237956,"timestamp":287139363058,"id":501,"parentId":397,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\navigate-reducer.js","layer":"app-pages-browser"},"startTime":1750489193839,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":250119,"timestamp":287139363996,"id":505,"parentId":397,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\server-action-reducer.js","layer":"app-pages-browser"},"startTime":1750489193840,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":252530,"timestamp":287139365026,"id":510,"parentId":334,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\index.js","layer":"pages-dir-browser"},"startTime":1750489193841,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":264725,"timestamp":287139364830,"id":509,"parentId":436,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\hydration-error-info.js","layer":"app-pages-browser"},"startTime":1750489193841,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":268091,"timestamp":287139365204,"id":511,"parentId":364,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\dev-indicator\\use-sync-dev-render-indicator.js","layer":"app-pages-browser"},"startTime":1750489193842,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":272461,"timestamp":287139364348,"id":507,"parentId":454,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\normalized-asset-prefix.js","layer":"app-pages-browser"},"startTime":1750489193841,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":97030,"timestamp":287139547877,"id":514,"parentId":410,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-status-code.js","layer":"app-pages-browser"},"startTime":1750489194024,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":99311,"timestamp":287139550235,"id":516,"parentId":411,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\segment.js","layer":"app-pages-browser"},"startTime":1750489194027,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":100592,"timestamp":287139551112,"id":517,"parentId":411,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\match-segments.js","layer":"app-pages-browser"},"startTime":1750489194028,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":104946,"timestamp":287139549657,"id":515,"parentId":419,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-status-code.js","layer":"pages-dir-browser"},"startTime":1750489194026,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":106035,"timestamp":287139551654,"id":519,"parentId":415,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\create-router-cache-key.js","layer":"app-pages-browser"},"startTime":1750489194028,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":118510,"timestamp":287139551820,"id":520,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\app-router-headers.js","layer":"app-pages-browser"},"startTime":1750489194028,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":122016,"timestamp":287139551962,"id":521,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\app\\app-dev-overlay.js","layer":"app-pages-browser"},"startTime":1750489194028,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":127636,"timestamp":287139551470,"id":518,"parentId":413,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js","layer":"app-pages-browser"},"startTime":1750489194028,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":514523,"timestamp":287139205746,"id":466,"parentId":279,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\index.js","layer":"pages-dir-browser"},"startTime":1750489193682,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":515551,"timestamp":287139208019,"id":469,"parentId":280,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_interop_require_default.js","layer":"pages-dir-browser"},"startTime":1750489193684,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":513456,"timestamp":287139211971,"id":470,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_interop_require_default.js","layer":"app-pages-browser"},"startTime":1750489193688,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":509398,"timestamp":287139219320,"id":472,"parentId":308,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_interop_require_wildcard.js","layer":"pages-dir-browser"},"startTime":1750489193696,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":512646,"timestamp":287139218412,"id":471,"parentId":286,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_interop_require_wildcard.js","layer":"app-pages-browser"},"startTime":1750489193695,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":421784,"timestamp":287139365406,"id":512,"parentId":360,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react\\cjs\\react-jsx-runtime.development.js","layer":"app-pages-browser"},"startTime":1750489193842,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62820,"timestamp":287139739184,"id":524,"parentId":476,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\parse-path.js","layer":"app-pages-browser"},"startTime":1750489194216,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":65738,"timestamp":287139738865,"id":523,"parentId":476,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\remove-trailing-slash.js","layer":"app-pages-browser"},"startTime":1750489194215,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":68809,"timestamp":287139739414,"id":525,"parentId":325,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\strip-ansi\\index.js","layer":"pages-dir-browser"},"startTime":1750489194216,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":82429,"timestamp":287139738381,"id":522,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js","layer":"app-pages-browser"},"startTime":1750489194215,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83358,"timestamp":287139739588,"id":526,"parentId":414,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\strip-ansi\\index.js","layer":"app-pages-browser"},"startTime":1750489194216,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":85028,"timestamp":287139739950,"id":528,"parentId":428,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\modern-browserslist-target.js","layer":"pages-dir-browser"},"startTime":1750489194216,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":86280,"timestamp":287139741002,"id":532,"parentId":447,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\error-source.js","layer":"pages-dir-browser"},"startTime":1750489194217,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":89142,"timestamp":287139741150,"id":533,"parentId":436,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\console-error.js","layer":"app-pages-browser"},"startTime":1750489194218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":96258,"timestamp":287139740797,"id":531,"parentId":438,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\is-hydration-error.js","layer":"pages-dir-browser"},"startTime":1750489194217,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":99542,"timestamp":287139741279,"id":534,"parentId":450,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\app-paths.js","layer":"pages-dir-browser"},"startTime":1750489194218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":102235,"timestamp":287139741375,"id":535,"parentId":474,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\app-paths.js","layer":"app-pages-browser"},"startTime":1750489194218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":104710,"timestamp":287139741472,"id":536,"parentId":436,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\enqueue-client-error.js","layer":"app-pages-browser"},"startTime":1750489194218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":120741,"timestamp":287139742191,"id":538,"parentId":459,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\css.js","layer":"pages-dir-browser"},"startTime":1750489194219,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":132922,"timestamp":287139742642,"id":539,"parentId":490,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\is-hydration-error.js","layer":"app-pages-browser"},"startTime":1750489194219,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":141505,"timestamp":287139741610,"id":537,"parentId":448,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\stack-frame.js","layer":"pages-dir-browser"},"startTime":1750489194218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":381288,"timestamp":287139547557,"id":513,"parentId":362,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-server-dom-webpack\\client.browser.js","layer":"app-pages-browser"},"startTime":1750489194024,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":50450,"timestamp":287139890955,"id":544,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\base.js","layer":"pages-dir-browser"},"startTime":1750489194367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":55868,"timestamp":287139890774,"id":543,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\shadow-portal.js","layer":"pages-dir-browser"},"startTime":1750489194367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":60290,"timestamp":287139889890,"id":541,"parentId":464,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\apply-flight-data.js","layer":"app-pages-browser"},"startTime":1750489194366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":68247,"timestamp":287139890458,"id":542,"parentId":464,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\fetch-server-response.js","layer":"app-pages-browser"},"startTime":1750489194367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":71156,"timestamp":287139891226,"id":546,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\css-reset.js","layer":"pages-dir-browser"},"startTime":1750489194368,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":75245,"timestamp":287139891098,"id":545,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\component-styles.js","layer":"pages-dir-browser"},"startTime":1750489194368,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":78358,"timestamp":287139891468,"id":548,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\colors.js","layer":"pages-dir-browser"},"startTime":1750489194368,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":81148,"timestamp":287139891348,"id":547,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\dark-theme.js","layer":"pages-dir-browser"},"startTime":1750489194368,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":90661,"timestamp":287139891613,"id":549,"parentId":437,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\lib\\console.js","layer":"app-pages-browser"},"startTime":1750489194368,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33619,"timestamp":287139988464,"id":550,"parentId":518,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils\\warn-once.js","layer":"app-pages-browser"},"startTime":1750489194465,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36721,"timestamp":287139989030,"id":551,"parentId":521,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\font\\font-styles.js","layer":"app-pages-browser"},"startTime":1750489194465,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":38212,"timestamp":287139990094,"id":556,"parentId":495,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\get-segment-value.js","layer":"app-pages-browser"},"startTime":1750489194467,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":42667,"timestamp":287139989272,"id":552,"parentId":521,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\dev-overlay.js","layer":"app-pages-browser"},"startTime":1750489194466,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":54925,"timestamp":287139990669,"id":558,"parentId":500,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\is-navigating-to-new-root-layout.js","layer":"app-pages-browser"},"startTime":1750489194467,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":58708,"timestamp":287139990416,"id":557,"parentId":495,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\navigation.react-server.js","layer":"app-pages-browser"},"startTime":1750489194467,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66159,"timestamp":287139990882,"id":559,"parentId":500,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\apply-router-state-patch-to-tree.js","layer":"app-pages-browser"},"startTime":1750489194467,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":358699,"timestamp":287139740568,"id":530,"parentId":452,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\error-telemetry-utils.js","layer":"pages-dir-browser"},"startTime":1750489194217,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":366292,"timestamp":287139740190,"id":529,"parentId":435,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\error-telemetry-utils.js","layer":"app-pages-browser"},"startTime":1750489194217,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":382056,"timestamp":287139743031,"id":540,"parentId":400,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react\\cjs\\react.development.js","layer":"app-pages-browser"},"startTime":1750489194219,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":73726,"timestamp":287140064031,"id":560,"parentId":495,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\server-inserted-html.shared-runtime.js","layer":"app-pages-browser"},"startTime":1750489194540,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":75765,"timestamp":287140065297,"id":563,"parentId":500,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\handle-mutable.js","layer":"app-pages-browser"},"startTime":1750489194542,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":78520,"timestamp":287140065027,"id":562,"parentId":503,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\handle-segment-mismatch.js","layer":"app-pages-browser"},"startTime":1750489194541,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":81029,"timestamp":287140065528,"id":564,"parentId":503,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\reducers\\has-interception-route-in-current-tree.js","layer":"app-pages-browser"},"startTime":1750489194542,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83180,"timestamp":287140066503,"id":568,"parentId":505,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\server-reference-info.js","layer":"app-pages-browser"},"startTime":1750489194543,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":86855,"timestamp":287140065746,"id":565,"parentId":534,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\segment.js","layer":"pages-dir-browser"},"startTime":1750489194542,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":88549,"timestamp":287140066774,"id":569,"parentId":505,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\assign-location.js","layer":"app-pages-browser"},"startTime":1750489194543,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":92279,"timestamp":287140067018,"id":570,"parentId":501,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\invalidate-cache-below-flight-segmentpath.js","layer":"app-pages-browser"},"startTime":1750489194543,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":108901,"timestamp":287140067280,"id":571,"parentId":501,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\should-hard-navigate.js","layer":"app-pages-browser"},"startTime":1750489194544,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":126136,"timestamp":287140064674,"id":561,"parentId":502,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\ppr-navigations.js","layer":"app-pages-browser"},"startTime":1750489194541,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":128977,"timestamp":287140067577,"id":572,"parentId":501,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\clear-cache-node-data-for-segment-path.js","layer":"app-pages-browser"},"startTime":1750489194544,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":142701,"timestamp":287140067773,"id":573,"parentId":501,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\aliased-prefetch-navigations.js","layer":"app-pages-browser"},"startTime":1750489194544,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":165069,"timestamp":287140067981,"id":574,"parentId":510,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\sorted-routes.js","layer":"pages-dir-browser"},"startTime":1750489194544,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":995666,"timestamp":287139739745,"id":527,"parentId":361,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-dom\\cjs\\react-dom-client.development.js","layer":"app-pages-browser"},"startTime":1750489194216,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":583459,"timestamp":287140166489,"id":576,"parentId":511,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\dev-indicator\\dev-render-indicator.js","layer":"app-pages-browser"},"startTime":1750489194643,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":800359,"timestamp":287139989475,"id":553,"parentId":333,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\index.js","layer":"pages-dir-browser"},"startTime":1750489194466,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":811422,"timestamp":287139989725,"id":554,"parentId":416,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\cjs\\react-jsx-runtime.development.js","layer":"pages-dir-browser"},"startTime":1750489194466,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":58418,"timestamp":287140754649,"id":577,"parentId":551,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\css.js","layer":"app-pages-browser"},"startTime":1750489195231,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":62136,"timestamp":287140756097,"id":579,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\base.js","layer":"app-pages-browser"},"startTime":1750489195233,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":68003,"timestamp":287140755287,"id":578,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\shadow-portal.js","layer":"app-pages-browser"},"startTime":1750489195232,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":70965,"timestamp":287140760126,"id":582,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\colors.js","layer":"app-pages-browser"},"startTime":1750489195237,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":74985,"timestamp":287140760376,"id":583,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\dark-theme.js","layer":"app-pages-browser"},"startTime":1750489195237,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":82586,"timestamp":287140758392,"id":580,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\component-styles.js","layer":"app-pages-browser"},"startTime":1750489195235,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":133560,"timestamp":287140760772,"id":585,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay\\error-overlay.js","layer":"pages-dir-browser"},"startTime":1750489195237,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":138703,"timestamp":287140759667,"id":581,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\css-reset.js","layer":"app-pages-browser"},"startTime":1750489195236,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":142220,"timestamp":287140761177,"id":587,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay\\error-overlay.js","layer":"app-pages-browser"},"startTime":1750489195238,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":155766,"timestamp":287140760984,"id":586,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\preferences.js","layer":"pages-dir-browser"},"startTime":1750489195237,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":166713,"timestamp":287140760584,"id":584,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-indicator.js","layer":"pages-dir-browser"},"startTime":1750489195237,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":167861,"timestamp":287140763710,"id":589,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\preferences.js","layer":"app-pages-browser"},"startTime":1750489195240,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":173086,"timestamp":287140764350,"id":590,"parentId":460,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\render-error.js","layer":"pages-dir-browser"},"startTime":1750489195241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":178137,"timestamp":287140764666,"id":591,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\render-error.js","layer":"app-pages-browser"},"startTime":1750489195241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":181008,"timestamp":287140764886,"id":592,"parentId":537,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\webpack-module-path.js","layer":"pages-dir-browser"},"startTime":1750489195241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":183786,"timestamp":287140765061,"id":593,"parentId":534,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\ensure-leading-slash.js","layer":"pages-dir-browser"},"startTime":1750489195241,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":187218,"timestamp":287140765209,"id":594,"parentId":535,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\ensure-leading-slash.js","layer":"app-pages-browser"},"startTime":1750489195242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":1377731,"timestamp":287139989933,"id":555,"parentId":417,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\cjs\\react-dom-client.development.js","layer":"pages-dir-browser"},"startTime":1750489194466,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":616239,"timestamp":287140765365,"id":595,"parentId":522,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\noop-turbopack-hmr.js","layer":"app-pages-browser"},"startTime":1750489195242,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":1328580,"timestamp":287140066116,"id":566,"parentId":455,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-is\\cjs\\react-is.development.js","layer":"pages-dir-browser"},"startTime":1750489194543,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":1346148,"timestamp":287140068124,"id":575,"parentId":466,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\cjs\\react.development.js","layer":"pages-dir-browser"},"startTime":1750489194545,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":1350844,"timestamp":287140066337,"id":567,"parentId":404,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-dom\\index.js","layer":"app-pages-browser"},"startTime":1750489194543,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":675385,"timestamp":287140761963,"id":588,"parentId":552,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-indicator.js","layer":"app-pages-browser"},"startTime":1750489195238,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":590250,"timestamp":287140849416,"id":596,"parentId":521,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\errors\\constants.js","layer":"app-pages-browser"},"startTime":1750489195326,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":592533,"timestamp":287140849920,"id":597,"parentId":542,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\set-cache-busting-search-param.js","layer":"app-pages-browser"},"startTime":1750489195326,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":117762,"timestamp":287141530478,"id":598,"parentId":541,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\fill-cache-with-new-subtree-data.js","layer":"app-pages-browser"},"startTime":1750489196007,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":115247,"timestamp":287141536586,"id":600,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\build-error.js","layer":"pages-dir-browser"},"startTime":1750489196013,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":115969,"timestamp":287141539490,"id":602,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\dev-tools-info.js","layer":"pages-dir-browser"},"startTime":1750489196016,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":119779,"timestamp":287141539042,"id":601,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\turbopack-info.js","layer":"pages-dir-browser"},"startTime":1750489196015,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":129335,"timestamp":287141534102,"id":599,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\errors.js","layer":"pages-dir-browser"},"startTime":1750489196011,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":121403,"timestamp":287141546050,"id":606,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\build-error.js","layer":"app-pages-browser"},"startTime":1750489196022,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":131213,"timestamp":287141539679,"id":603,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\route-info.js","layer":"pages-dir-browser"},"startTime":1750489196016,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":127307,"timestamp":287141546612,"id":608,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\dev-tools-info.js","layer":"app-pages-browser"},"startTime":1750489196023,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":139574,"timestamp":287141540430,"id":604,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\user-preferences.js","layer":"pages-dir-browser"},"startTime":1750489196017,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":134701,"timestamp":287141548592,"id":610,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\route-info.js","layer":"app-pages-browser"},"startTime":1750489196025,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":142042,"timestamp":287141546413,"id":607,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\errors.js","layer":"app-pages-browser"},"startTime":1750489196023,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":150293,"timestamp":287141546754,"id":609,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\turbopack-info.js","layer":"app-pages-browser"},"startTime":1750489196023,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":150354,"timestamp":287141549157,"id":612,"parentId":557,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\not-found.js","layer":"app-pages-browser"},"startTime":1750489196026,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":152530,"timestamp":287141549815,"id":614,"parentId":557,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\unstable-rethrow.js","layer":"app-pages-browser"},"startTime":1750489196026,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":155210,"timestamp":287141550052,"id":615,"parentId":557,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\unauthorized.js","layer":"app-pages-browser"},"startTime":1750489196026,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":162549,"timestamp":287141548968,"id":611,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\user-preferences.js","layer":"app-pages-browser"},"startTime":1750489196025,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":164321,"timestamp":287141549361,"id":613,"parentId":557,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\forbidden.js","layer":"app-pages-browser"},"startTime":1750489196026,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":166357,"timestamp":287141550226,"id":616,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\code-frame\\code-frame.js","layer":"pages-dir-browser"},"startTime":1750489196027,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":162641,"timestamp":287141555972,"id":618,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\styles.js","layer":"pages-dir-browser"},"startTime":1750489196032,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":161873,"timestamp":287141558602,"id":619,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\styles.js","layer":"app-pages-browser"},"startTime":1750489196035,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":169097,"timestamp":287141554760,"id":617,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\code-frame\\code-frame.js","layer":"app-pages-browser"},"startTime":1750489196031,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":227778,"timestamp":287141563340,"id":620,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-layout\\error-overlay-layout.js","layer":"pages-dir-browser"},"startTime":1750489196040,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":232467,"timestamp":287141565334,"id":621,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-layout\\error-overlay-layout.js","layer":"app-pages-browser"},"startTime":1750489196042,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":378370,"timestamp":287141545669,"id":605,"parentId":513,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-server-dom-webpack\\cjs\\react-server-dom-webpack-client.browser.development.js","layer":"app-pages-browser"},"startTime":1750489196022,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":73885,"timestamp":287141865360,"id":623,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-pagination\\error-overlay-pagination.js","layer":"app-pages-browser"},"startTime":1750489196342,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":77045,"timestamp":287141865474,"id":624,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\editor-link.js","layer":"pages-dir-browser"},"startTime":1750489196342,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":80777,"timestamp":287141865090,"id":622,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-pagination\\error-overlay-pagination.js","layer":"pages-dir-browser"},"startTime":1750489196342,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":85314,"timestamp":287141865567,"id":625,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\terminal.js","layer":"pages-dir-browser"},"startTime":1750489196342,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36358,"timestamp":287142005286,"id":627,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\editor-link.js","layer":"app-pages-browser"},"startTime":1750489196482,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":47485,"timestamp":287142004838,"id":626,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\terminal.js","layer":"app-pages-browser"},"startTime":1750489196481,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":50340,"timestamp":287142005518,"id":628,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\call-stack-frame\\call-stack-frame.js","layer":"pages-dir-browser"},"startTime":1750489196482,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":55774,"timestamp":287142005697,"id":629,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\call-stack-frame\\call-stack-frame.js","layer":"app-pages-browser"},"startTime":1750489196482,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":59303,"timestamp":287142005884,"id":630,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-footer\\error-overlay-footer.js","layer":"pages-dir-browser"},"startTime":1750489196482,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":64440,"timestamp":287142006039,"id":631,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-footer\\error-overlay-footer.js","layer":"app-pages-browser"},"startTime":1750489196482,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":67646,"timestamp":287142006202,"id":632,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\version-staleness-info\\version-staleness-info.js","layer":"pages-dir-browser"},"startTime":1750489196483,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":69840,"timestamp":287142006512,"id":634,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\environment-name-label\\environment-name-label.js","layer":"pages-dir-browser"},"startTime":1750489196483,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":73831,"timestamp":287142006648,"id":635,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\environment-name-label\\environment-name-label.js","layer":"app-pages-browser"},"startTime":1750489196483,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":88374,"timestamp":287142006346,"id":633,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\version-staleness-info\\version-staleness-info.js","layer":"app-pages-browser"},"startTime":1750489196483,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":95235,"timestamp":287142006824,"id":636,"parentId":584,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\dev-indicator\\dev-render-indicator.js","layer":"pages-dir-browser"},"startTime":1750489196483,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":91760,"timestamp":287142012547,"id":645,"parentId":607,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\error-source.js","layer":"app-pages-browser"},"startTime":1750489196489,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":98602,"timestamp":287142008804,"id":642,"parentId":591,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\get-error-by-type.js","layer":"app-pages-browser"},"startTime":1750489196485,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":99241,"timestamp":287142012908,"id":646,"parentId":599,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\console-error.js","layer":"pages-dir-browser"},"startTime":1750489196489,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":136755,"timestamp":287142013951,"id":650,"parentId":588,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\utils.js","layer":"app-pages-browser"},"startTime":1750489196490,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":140731,"timestamp":287142013704,"id":649,"parentId":584,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\utils.js","layer":"pages-dir-browser"},"startTime":1750489196490,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":124576,"timestamp":287142031233,"id":655,"parentId":597,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\hash.js","layer":"app-pages-browser"},"startTime":1750489196508,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":128057,"timestamp":287142030996,"id":654,"parentId":617,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\stack-frame.js","layer":"app-pages-browser"},"startTime":1750489196507,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":132737,"timestamp":287142031415,"id":656,"parentId":585,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-delayed-render.js","layer":"pages-dir-browser"},"startTime":1750489196508,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":135543,"timestamp":287142031584,"id":657,"parentId":587,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-delayed-render.js","layer":"app-pages-browser"},"startTime":1750489196508,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":143082,"timestamp":287142030344,"id":652,"parentId":588,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\next-logo.js","layer":"app-pages-browser"},"startTime":1750489196507,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":168417,"timestamp":287142014117,"id":651,"parentId":584,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\next-logo.js","layer":"pages-dir-browser"},"startTime":1750489196491,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":153941,"timestamp":287142031880,"id":659,"parentId":588,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\gear-icon.js","layer":"app-pages-browser"},"startTime":1750489196508,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":156130,"timestamp":287142031724,"id":658,"parentId":584,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\gear-icon.js","layer":"pages-dir-browser"},"startTime":1750489196508,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":158266,"timestamp":287142032067,"id":660,"parentId":598,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\router-reducer\\invalidate-cache-by-router-state.js","layer":"app-pages-browser"},"startTime":1750489196508,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":159850,"timestamp":287142034809,"id":661,"parentId":599,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\component-stack-pseudo-html.js","layer":"pages-dir-browser"},"startTime":1750489196511,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":163038,"timestamp":287142035261,"id":662,"parentId":607,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\component-stack-pseudo-html.js","layer":"app-pages-browser"},"startTime":1750489196512,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":167695,"timestamp":287142035490,"id":663,"parentId":604,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\dark-icon.js","layer":"pages-dir-browser"},"startTime":1750489196512,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":307688,"timestamp":287142010390,"id":643,"parentId":459,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_tagged_template_literal_loose.js","layer":"pages-dir-browser"},"startTime":1750489196487,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":308098,"timestamp":287142012014,"id":644,"parentId":551,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_tagged_template_literal_loose.js","layer":"app-pages-browser"},"startTime":1750489196488,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":322260,"timestamp":287142008030,"id":641,"parentId":553,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\cjs\\react-dom.development.js","layer":"pages-dir-browser"},"startTime":1750489196484,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":322304,"timestamp":287142013164,"id":647,"parentId":444,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\stacktrace-parser\\stack-trace-parser.cjs.js","layer":"pages-dir-browser"},"startTime":1750489196490,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":326937,"timestamp":287142013328,"id":648,"parentId":490,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\stacktrace-parser\\stack-trace-parser.cjs.js","layer":"app-pages-browser"},"startTime":1750489196490,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":338270,"timestamp":287142006974,"id":637,"parentId":423,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_class_private_field_loose_base.js","layer":"pages-dir-browser"},"startTime":1750489196483,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":339958,"timestamp":287142007213,"id":638,"parentId":499,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_class_private_field_loose_base.js","layer":"app-pages-browser"},"startTime":1750489196484,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":343195,"timestamp":287142007555,"id":639,"parentId":423,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_class_private_field_loose_key.js","layer":"pages-dir-browser"},"startTime":1750489196484,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":344189,"timestamp":287142007738,"id":640,"parentId":499,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_class_private_field_loose_key.js","layer":"app-pages-browser"},"startTime":1750489196484,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":79950,"timestamp":287142284169,"id":664,"parentId":611,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\dark-icon.js","layer":"app-pages-browser"},"startTime":1750489196761,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":82089,"timestamp":287142285113,"id":666,"parentId":611,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\eye-icon.js","layer":"app-pages-browser"},"startTime":1750489196762,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":86045,"timestamp":287142285304,"id":667,"parentId":604,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\light-icon.js","layer":"pages-dir-browser"},"startTime":1750489196762,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":88847,"timestamp":287142285505,"id":668,"parentId":611,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\light-icon.js","layer":"app-pages-browser"},"startTime":1750489196762,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":102105,"timestamp":287142285884,"id":670,"parentId":611,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\system-icon.js","layer":"app-pages-browser"},"startTime":1750489196762,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":105179,"timestamp":287142285708,"id":669,"parentId":604,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\system-icon.js","layer":"pages-dir-browser"},"startTime":1750489196762,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":110066,"timestamp":287142286046,"id":671,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\index.js","layer":"pages-dir-browser"},"startTime":1750489196762,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":115361,"timestamp":287142284844,"id":665,"parentId":604,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\eye-icon.js","layer":"pages-dir-browser"},"startTime":1750489196761,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":117319,"timestamp":287142286179,"id":672,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\index.js","layer":"app-pages-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":119125,"timestamp":287142286364,"id":673,"parentId":614,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\unstable-rethrow.browser.js","layer":"app-pages-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":122094,"timestamp":287142286524,"id":674,"parentId":616,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\code-frame\\parse-code-frame.js","layer":"pages-dir-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":127269,"timestamp":287142286639,"id":675,"parentId":617,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\code-frame\\parse-code-frame.js","layer":"app-pages-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":133075,"timestamp":287142286761,"id":676,"parentId":616,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\file.js","layer":"pages-dir-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":135845,"timestamp":287142286879,"id":677,"parentId":617,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\file.js","layer":"app-pages-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":137479,"timestamp":287142287180,"id":679,"parentId":617,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\external.js","layer":"app-pages-browser"},"startTime":1750489196764,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":140874,"timestamp":287142287008,"id":678,"parentId":616,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\external.js","layer":"pages-dir-browser"},"startTime":1750489196763,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":142531,"timestamp":287142287616,"id":681,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\index.js","layer":"app-pages-browser"},"startTime":1750489196764,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":145363,"timestamp":287142287403,"id":680,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\index.js","layer":"pages-dir-browser"},"startTime":1750489196764,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":148718,"timestamp":287142287991,"id":683,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-bottom-stack\\index.js","layer":"app-pages-browser"},"startTime":1750489196764,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":150846,"timestamp":287142287819,"id":682,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-bottom-stack\\index.js","layer":"pages-dir-browser"},"startTime":1750489196764,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":152343,"timestamp":287142288378,"id":685,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\index.js","layer":"app-pages-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":154867,"timestamp":287142288192,"id":684,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\index.js","layer":"pages-dir-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":159718,"timestamp":287142288636,"id":687,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\copy-button\\index.js","layer":"app-pages-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":162009,"timestamp":287142288764,"id":688,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\fader\\index.js","layer":"pages-dir-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":164887,"timestamp":287142288889,"id":689,"parentId":580,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\fader\\index.js","layer":"app-pages-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":168962,"timestamp":287142288519,"id":686,"parentId":545,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\copy-button\\index.js","layer":"pages-dir-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":172901,"timestamp":287142289025,"id":690,"parentId":616,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\use-open-in-editor.js","layer":"pages-dir-browser"},"startTime":1750489196765,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":178023,"timestamp":287142289191,"id":691,"parentId":617,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\use-open-in-editor.js","layer":"app-pages-browser"},"startTime":1750489196766,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":180803,"timestamp":287142289329,"id":692,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-message\\error-message.js","layer":"pages-dir-browser"},"startTime":1750489196766,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":188876,"timestamp":287142289457,"id":693,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-message\\error-message.js","layer":"app-pages-browser"},"startTime":1750489196766,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":191780,"timestamp":287142289592,"id":694,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\error-overlay-toolbar.js","layer":"pages-dir-browser"},"startTime":1750489196766,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":193809,"timestamp":287142289722,"id":695,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\error-overlay-toolbar.js","layer":"app-pages-browser"},"startTime":1750489196766,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":196373,"timestamp":287142289929,"id":696,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-type-label\\error-type-label.js","layer":"pages-dir-browser"},"startTime":1750489196766,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":199396,"timestamp":287142290108,"id":697,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-type-label\\error-type-label.js","layer":"app-pages-browser"},"startTime":1750489196767,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":204223,"timestamp":287142290274,"id":698,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-nav\\error-overlay-nav.js","layer":"pages-dir-browser"},"startTime":1750489196767,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":210506,"timestamp":287142290434,"id":699,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-nav\\error-overlay-nav.js","layer":"app-pages-browser"},"startTime":1750489196767,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":213569,"timestamp":287142290695,"id":701,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\dialog.js","layer":"app-pages-browser"},"startTime":1750489196767,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":216971,"timestamp":287142290568,"id":700,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\dialog.js","layer":"pages-dir-browser"},"startTime":1750489196767,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":219687,"timestamp":287142290926,"id":702,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\header.js","layer":"pages-dir-browser"},"startTime":1750489196767,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":222581,"timestamp":287142291111,"id":703,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\header.js","layer":"app-pages-browser"},"startTime":1750489196768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":287660,"timestamp":287142291446,"id":705,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\body.js","layer":"app-pages-browser"},"startTime":1750489196768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":290056,"timestamp":287142291287,"id":704,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\body.js","layer":"pages-dir-browser"},"startTime":1750489196768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":307737,"timestamp":287142291615,"id":706,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\call-stack\\call-stack.js","layer":"pages-dir-browser"},"startTime":1750489196768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":311242,"timestamp":287142292085,"id":709,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\overlay\\overlay.js","layer":"app-pages-browser"},"startTime":1750489196769,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":347673,"timestamp":287142291761,"id":707,"parentId":621,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\call-stack\\call-stack.js","layer":"app-pages-browser"},"startTime":1750489196768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":355569,"timestamp":287142291940,"id":708,"parentId":620,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\overlay\\overlay.js","layer":"pages-dir-browser"},"startTime":1750489196768,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":357619,"timestamp":287142292383,"id":711,"parentId":622,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\left-arrow.js","layer":"pages-dir-browser"},"startTime":1750489196769,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":383085,"timestamp":287142292249,"id":710,"parentId":623,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\left-arrow.js","layer":"app-pages-browser"},"startTime":1750489196769,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":650588,"timestamp":287142030785,"id":653,"parentId":567,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-dom\\cjs\\react-dom.development.js","layer":"app-pages-browser"},"startTime":1750489196507,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":408359,"timestamp":287142292539,"id":712,"parentId":623,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\right-arrow.js","layer":"app-pages-browser"},"startTime":1750489196769,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":410140,"timestamp":287142292682,"id":713,"parentId":622,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\right-arrow.js","layer":"pages-dir-browser"},"startTime":1750489196769,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":322759,"timestamp":287142383788,"id":714,"parentId":654,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\webpack-module-path.js","layer":"app-pages-browser"},"startTime":1750489196860,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26635,"timestamp":287142840416,"id":717,"parentId":652,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\merge-refs.js","layer":"app-pages-browser"},"startTime":1750489197317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":29806,"timestamp":287142840195,"id":716,"parentId":651,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\use-minimum-loading-time-multiple.js","layer":"pages-dir-browser"},"startTime":1750489197317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33280,"timestamp":287142839652,"id":715,"parentId":652,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\use-minimum-loading-time-multiple.js","layer":"app-pages-browser"},"startTime":1750489197316,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34958,"timestamp":287142840640,"id":718,"parentId":651,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\merge-refs.js","layer":"pages-dir-browser"},"startTime":1750489197317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37534,"timestamp":287142841046,"id":720,"parentId":633,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\cx.js","layer":"app-pages-browser"},"startTime":1750489197317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39056,"timestamp":287142840830,"id":719,"parentId":632,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\cx.js","layer":"pages-dir-browser"},"startTime":1750489197317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":44336,"timestamp":287142882220,"id":721,"parentId":600,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\index.js","layer":"pages-dir-browser"},"startTime":1750489197359,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":47514,"timestamp":287142882700,"id":724,"parentId":607,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\index.js","layer":"app-pages-browser"},"startTime":1750489197359,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":53775,"timestamp":287142882613,"id":723,"parentId":599,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\index.js","layer":"pages-dir-browser"},"startTime":1750489197359,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":57726,"timestamp":287142882497,"id":722,"parentId":606,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\index.js","layer":"app-pages-browser"},"startTime":1750489197359,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62743,"timestamp":287142882789,"id":725,"parentId":630,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-footer\\error-feedback\\error-feedback.js","layer":"pages-dir-browser"},"startTime":1750489197359,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":64148,"timestamp":287142884152,"id":726,"parentId":631,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-footer\\error-feedback\\error-feedback.js","layer":"app-pages-browser"},"startTime":1750489197361,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66145,"timestamp":287142885386,"id":727,"parentId":681,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\styles.js","layer":"app-pages-browser"},"startTime":1750489197365,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":65112,"timestamp":287142888804,"id":728,"parentId":680,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\styles.js","layer":"pages-dir-browser"},"startTime":1750489197365,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":67395,"timestamp":287142889043,"id":729,"parentId":681,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\toast.js","layer":"app-pages-browser"},"startTime":1750489197365,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":70276,"timestamp":287142889265,"id":730,"parentId":680,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\toast.js","layer":"pages-dir-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":139203,"timestamp":287142889769,"id":733,"parentId":685,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-body.js","layer":"app-pages-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":142257,"timestamp":287142889884,"id":734,"parentId":684,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-body.js","layer":"pages-dir-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":146522,"timestamp":287142889655,"id":732,"parentId":684,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog.js","layer":"pages-dir-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":151447,"timestamp":287142889506,"id":731,"parentId":685,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog.js","layer":"app-pages-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":155635,"timestamp":287142890065,"id":736,"parentId":684,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-header.js","layer":"pages-dir-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":157740,"timestamp":287142890146,"id":737,"parentId":685,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-content.js","layer":"app-pages-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":160020,"timestamp":287142889971,"id":735,"parentId":685,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-header.js","layer":"app-pages-browser"},"startTime":1750489197366,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":162704,"timestamp":287142890244,"id":738,"parentId":684,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-content.js","layer":"pages-dir-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":164761,"timestamp":287142890597,"id":742,"parentId":684,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\styles.js","layer":"pages-dir-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":167529,"timestamp":287142890509,"id":741,"parentId":685,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\styles.js","layer":"app-pages-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":171949,"timestamp":287142890414,"id":740,"parentId":695,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\copy-stack-trace-button.js","layer":"app-pages-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":174041,"timestamp":287142890327,"id":739,"parentId":694,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\copy-stack-trace-button.js","layer":"pages-dir-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":176075,"timestamp":287142890840,"id":745,"parentId":694,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\docs-link-button.js","layer":"pages-dir-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":179993,"timestamp":287142890922,"id":746,"parentId":695,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\docs-link-button.js","layer":"app-pages-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":190648,"timestamp":287142890670,"id":743,"parentId":694,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\nodejs-inspector-button.js","layer":"pages-dir-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":170182,"timestamp":287142916220,"id":747,"parentId":709,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\overlay.js","layer":"app-pages-browser"},"startTime":1750489197393,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":205818,"timestamp":287142890748,"id":744,"parentId":695,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\nodejs-inspector-button.js","layer":"app-pages-browser"},"startTime":1750489197367,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":178918,"timestamp":287142920602,"id":748,"parentId":708,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\overlay.js","layer":"pages-dir-browser"},"startTime":1750489197397,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":146648,"timestamp":287142965621,"id":753,"parentId":607,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\hot-linked-text\\index.js","layer":"app-pages-browser"},"startTime":1750489197442,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":151204,"timestamp":287142964684,"id":750,"parentId":661,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\hydration-diff\\diff-view.js","layer":"pages-dir-browser"},"startTime":1750489197441,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":155082,"timestamp":287142965197,"id":751,"parentId":662,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\hydration-diff\\diff-view.js","layer":"app-pages-browser"},"startTime":1750489197442,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":160036,"timestamp":287142965411,"id":752,"parentId":599,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\hot-linked-text\\index.js","layer":"pages-dir-browser"},"startTime":1750489197442,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":314905,"timestamp":287142964156,"id":749,"parentId":527,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\scheduler\\index.js","layer":"app-pages-browser"},"startTime":1750489197441,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":32351,"timestamp":287143320015,"id":757,"parentId":753,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\magic-identifier.js","layer":"app-pages-browser"},"startTime":1750489197796,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37893,"timestamp":287143320775,"id":760,"parentId":731,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-on-click-outside.js","layer":"app-pages-browser"},"startTime":1750489197797,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":52004,"timestamp":287143320636,"id":759,"parentId":732,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-on-click-outside.js","layer":"pages-dir-browser"},"startTime":1750489197797,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":61178,"timestamp":287143320437,"id":758,"parentId":752,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\magic-identifier.js","layer":"pages-dir-browser"},"startTime":1750489197797,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66152,"timestamp":287143321208,"id":763,"parentId":750,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\collapse-icon.js","layer":"pages-dir-browser"},"startTime":1750489197798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":69829,"timestamp":287143320940,"id":761,"parentId":732,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-measure-height.js","layer":"pages-dir-browser"},"startTime":1750489197797,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":74361,"timestamp":287143321073,"id":762,"parentId":731,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-measure-height.js","layer":"app-pages-browser"},"startTime":1750489197797,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":77611,"timestamp":287143321552,"id":766,"parentId":746,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\parse-url-from-text.js","layer":"app-pages-browser"},"startTime":1750489197798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":80101,"timestamp":287143322147,"id":768,"parentId":748,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\body-locker.js","layer":"pages-dir-browser"},"startTime":1750489197799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83509,"timestamp":287143321932,"id":767,"parentId":747,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\body-locker.js","layer":"app-pages-browser"},"startTime":1750489197798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":89810,"timestamp":287143321439,"id":765,"parentId":745,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\parse-url-from-text.js","layer":"pages-dir-browser"},"startTime":1750489197798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":92134,"timestamp":287143321321,"id":764,"parentId":751,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\collapse-icon.js","layer":"app-pages-browser"},"startTime":1750489197798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":104646,"timestamp":287143328022,"id":773,"parentId":725,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\thumbs\\thumbs-down.js","layer":"pages-dir-browser"},"startTime":1750489197804,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":110908,"timestamp":287143327339,"id":771,"parentId":725,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\thumbs\\thumbs-up.js","layer":"pages-dir-browser"},"startTime":1750489197804,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":114828,"timestamp":287143328170,"id":774,"parentId":726,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\thumbs\\thumbs-down.js","layer":"app-pages-browser"},"startTime":1750489197805,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":124092,"timestamp":287143327813,"id":772,"parentId":726,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\thumbs\\thumbs-up.js","layer":"app-pages-browser"},"startTime":1750489197804,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":145680,"timestamp":287143315925,"id":754,"parentId":555,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\polyfills\\process.js","layer":"pages-dir-browser"},"startTime":1750489197792,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":146761,"timestamp":287143316386,"id":755,"parentId":540,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\polyfills\\process.js","layer":"app-pages-browser"},"startTime":1750489197793,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":147142,"timestamp":287143316881,"id":756,"parentId":555,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\scheduler\\index.js","layer":"pages-dir-browser"},"startTime":1750489197793,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":172617,"timestamp":287143322295,"id":769,"parentId":625,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\anser\\index.js","layer":"pages-dir-browser"},"startTime":1750489197799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":180306,"timestamp":287143325487,"id":770,"parentId":626,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\anser\\index.js","layer":"app-pages-browser"},"startTime":1750489197802,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":11916,"timestamp":287143506282,"id":775,"parentId":749,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\scheduler\\cjs\\scheduler.development.js","layer":"app-pages-browser"},"startTime":1750489197983,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":28819,"timestamp":287143524095,"id":776,"parentId":756,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\scheduler\\cjs\\scheduler.development.js","layer":"pages-dir-browser"},"startTime":1750489198001,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":14015,"timestamp":287143588169,"id":777,"parentId":754,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\process\\browser.js","layer":"pages-dir-browser"},"startTime":1750489198065,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":17476,"timestamp":287143588479,"id":778,"parentId":755,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\process\\browser.js","layer":"app-pages-browser"},"startTime":1750489198065,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":7685849,"timestamp":287135920487,"id":271,"parentId":265,"tags":{"request":"next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":7686048,"timestamp":287135920319,"id":267,"parentId":265,"tags":{"request":"./node_modules/next/dist/client/dev/amp-dev"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":7685872,"timestamp":287135920506,"id":272,"parentId":265,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\router.js"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":7685996,"timestamp":287135920530,"id":273,"parentId":265,"tags":{"request":"next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":7686156,"timestamp":287135920385,"id":268,"parentId":265,"tags":{"request":"./node_modules/next/dist/client/next-dev.js"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":7686089,"timestamp":287135920461,"id":270,"parentId":265,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750489190397,"traceId":"901c6e83d477b378"},{"name":"make","duration":7687439,"timestamp":287135919584,"id":265,"parentId":264,"tags":{},"startTime":1750489190396,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":24765,"timestamp":287143638768,"id":780,"parentId":779,"tags":{},"startTime":1750489198115,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":37,"timestamp":287143663734,"id":782,"parentId":779,"tags":{},"startTime":1750489198140,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":306,"timestamp":287143663844,"id":783,"parentId":779,"tags":{},"startTime":1750489198140,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":37,"timestamp":287143664245,"id":784,"parentId":779,"tags":{},"startTime":1750489198141,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":31,"timestamp":287143664368,"id":785,"parentId":779,"tags":{},"startTime":1750489198141,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":843,"timestamp":287143663671,"id":781,"parentId":779,"tags":{},"startTime":1750489198140,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":83545,"timestamp":287143702466,"id":786,"parentId":779,"tags":{},"startTime":1750489198179,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":161899,"timestamp":287143786109,"id":787,"parentId":779,"tags":{},"startTime":1750489198263,"traceId":"901c6e83d477b378"},{"name":"hash","duration":70638,"timestamp":287143962839,"id":788,"parentId":779,"tags":{},"startTime":1750489198439,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":2122,"timestamp":287144033468,"id":789,"parentId":779,"tags":{},"startTime":1750489198510,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":493,"timestamp":287144035384,"id":790,"parentId":779,"tags":{},"startTime":1750489198512,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":685246,"timestamp":287144035921,"id":791,"parentId":779,"tags":{},"startTime":1750489198512,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":883,"timestamp":287144736280,"id":792,"parentId":264,"tags":{},"startTime":1750489199213,"traceId":"901c6e83d477b378"},{"name":"seal","duration":1115765,"timestamp":287143633591,"id":779,"parentId":264,"tags":{},"startTime":1750489198110,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":8832379,"timestamp":287135917440,"id":264,"parentId":3,"tags":{"name":"client"},"startTime":1750489190394,"traceId":"901c6e83d477b378"},{"name":"emit","duration":269439,"timestamp":287144752625,"id":793,"parentId":3,"tags":{},"startTime":1750489199229,"traceId":"901c6e83d477b378"},{"name":"next-client-pages-loader","duration":177,"timestamp":287145286398,"id":814,"parentId":813,"tags":{"absolutePagePath":"private-next-pages/_app"},"startTime":1750489199763,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":25404,"timestamp":287145279401,"id":813,"parentId":804,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_app&page=%2F_app!","layer":"pages-dir-browser"},"startTime":1750489199756,"traceId":"901c6e83d477b378"},{"name":"next-client-pages-loader","duration":124,"timestamp":287145305171,"id":816,"parentId":815,"tags":{"absolutePagePath":"private-next-pages/_error"},"startTime":1750489199782,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":1136,"timestamp":287145304923,"id":815,"parentId":806,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-client-pages-loader.js?absolutePagePath=private-next-pages%2F_error&page=%2F_error!","layer":"pages-dir-browser"},"startTime":1750489199781,"traceId":"901c6e83d477b378"},{"name":"next-client-pages-loader","duration":306,"timestamp":287145306350,"id":818,"parentId":817,"tags":{"absolutePagePath":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_error.js"},"startTime":1750489199783,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":2785,"timestamp":287145306131,"id":817,"parentId":810,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!","layer":"pages-dir-browser"},"startTime":1750489199783,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":223805,"timestamp":287145107323,"id":802,"parentId":799,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":224593,"timestamp":287145107740,"id":811,"parentId":799,"tags":{"request":"next-client-pages-loader?absolutePagePath=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":123578,"timestamp":287145266027,"id":812,"parentId":800,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js","layer":"pages-dir-browser"},"startTime":1750489199742,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":307475,"timestamp":287145107524,"id":809,"parentId":799,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":324686,"timestamp":287145107484,"id":807,"parentId":799,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":334245,"timestamp":287145107418,"id":803,"parentId":799,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":9141,"timestamp":287145463789,"id":822,"parentId":812,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\internal\\helpers.js","layer":"pages-dir-browser"},"startTime":1750489199940,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":10111,"timestamp":287145463967,"id":823,"parentId":812,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-refresh\\runtime.js","layer":"pages-dir-browser"},"startTime":1750489199940,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":21477,"timestamp":287145463401,"id":820,"parentId":801,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\next-dev.js","layer":"pages-dir-browser"},"startTime":1750489199940,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26435,"timestamp":287145462968,"id":819,"parentId":817,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_error.js","layer":"pages-dir-browser"},"startTime":1750489199939,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":32287,"timestamp":287145463625,"id":821,"parentId":805,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\router.js","layer":"pages-dir-browser"},"startTime":1750489199940,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":411146,"timestamp":287145107512,"id":808,"parentId":799,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15246,"timestamp":287145523720,"id":825,"parentId":820,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\webpack.js","layer":"pages-dir-browser"},"startTime":1750489200000,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":21033,"timestamp":287145524356,"id":827,"parentId":821,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\with-router.js","layer":"pages-dir-browser"},"startTime":1750489200001,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34081,"timestamp":287145524116,"id":826,"parentId":820,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\page-bootstrap.js","layer":"pages-dir-browser"},"startTime":1750489200001,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34624,"timestamp":287145527892,"id":829,"parentId":820,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\hot-middleware-client.js","layer":"pages-dir-browser"},"startTime":1750489200004,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":53805,"timestamp":287145520449,"id":824,"parentId":823,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-refresh\\cjs\\react-refresh-runtime.development.js","layer":"pages-dir-browser"},"startTime":1750489199997,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":67744,"timestamp":287145524699,"id":828,"parentId":820,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\index.js","layer":"pages-dir-browser"},"startTime":1750489200001,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":488372,"timestamp":287145106487,"id":800,"parentId":799,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489199583,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":14951,"timestamp":287145603570,"id":832,"parentId":821,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200080,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":95488,"timestamp":287145528407,"id":830,"parentId":821,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-error.js","layer":"pages-dir-browser"},"startTime":1750489200005,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36249,"timestamp":287145602804,"id":831,"parentId":819,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head.js","layer":"pages-dir-browser"},"startTime":1750489200079,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":23209,"timestamp":287145664045,"id":835,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\fouc.js","layer":"pages-dir-browser"},"startTime":1750489200140,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":123139,"timestamp":287145603877,"id":833,"parentId":821,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\router.js","layer":"pages-dir-browser"},"startTime":1750489200080,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66729,"timestamp":287145664300,"id":836,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\on-demand-entries-client.js","layer":"pages-dir-browser"},"startTime":1750489200141,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":106915,"timestamp":287145674491,"id":840,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\route-announcer.js","layer":"pages-dir-browser"},"startTime":1750489200151,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":113664,"timestamp":287145674296,"id":839,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\head-manager.js","layer":"pages-dir-browser"},"startTime":1750489200151,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":120330,"timestamp":287145673957,"id":838,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\page-loader.js","layer":"pages-dir-browser"},"startTime":1750489200150,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":122156,"timestamp":287145675077,"id":842,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\remove-base-path.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":124315,"timestamp":287145675284,"id":843,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\has-base-path.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":126460,"timestamp":287145675646,"id":845,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head-manager-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":135918,"timestamp":287145674868,"id":841,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\script.js","layer":"pages-dir-browser"},"startTime":1750489200151,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":139393,"timestamp":287145675457,"id":844,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_app.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":141188,"timestamp":287145675811,"id":846,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\runtime-config.external.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":142623,"timestamp":287145676308,"id":849,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\image-config-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200153,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":146091,"timestamp":287145675942,"id":847,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\mitt.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":149671,"timestamp":287145676527,"id":850,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\app-router-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200153,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":720244,"timestamp":287145107469,"id":806,"parentId":799,"tags":{"request":"next-client-pages-loader?absolutePagePath=private-next-pages%2F_error&page=%2F_error!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":155809,"timestamp":287145676714,"id":851,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\hooks-client-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200153,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":162396,"timestamp":287145676067,"id":848,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils.js","layer":"pages-dir-browser"},"startTime":1750489200152,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":167637,"timestamp":287145676943,"id":852,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\adapters.js","layer":"pages-dir-browser"},"startTime":1750489200153,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":98737,"timestamp":287145750921,"id":854,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\handle-smooth-scroll.js","layer":"pages-dir-browser"},"startTime":1750489200227,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":102939,"timestamp":287145750336,"id":853,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\querystring.js","layer":"pages-dir-browser"},"startTime":1750489200227,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":104458,"timestamp":287145751198,"id":855,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-dynamic.js","layer":"pages-dir-browser"},"startTime":1750489200228,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":773881,"timestamp":287145107438,"id":804,"parentId":799,"tags":{"request":"next-client-pages-loader?absolutePagePath=private-next-pages%2F_app&page=%2F_app!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":220565,"timestamp":287145663795,"id":834,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\require-instrumentation-client.js","layer":"pages-dir-browser"},"startTime":1750489200140,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":20713,"timestamp":287145870047,"id":856,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\initialize-for-page-router.js","layer":"pages-dir-browser"},"startTime":1750489200346,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":24930,"timestamp":287145870484,"id":857,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\dev-build-indicator.js","layer":"pages-dir-browser"},"startTime":1750489200347,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":238964,"timestamp":287145673686,"id":837,"parentId":825,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\deployment-id.js","layer":"pages-dir-browser"},"startTime":1750489200150,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39261,"timestamp":287145899378,"id":859,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\runtime-error-handler.js","layer":"pages-dir-browser"},"startTime":1750489200376,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":42121,"timestamp":287145898831,"id":858,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\is-next-router-error.js","layer":"pages-dir-browser"},"startTime":1750489200375,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":2906,"timestamp":287145949816,"id":874,"parentId":833,"tags":{"layer":null},"startTime":1750489200426,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":67772,"timestamp":287145899621,"id":860,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\shared.js","layer":"pages-dir-browser"},"startTime":1750489200376,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":73812,"timestamp":287145944482,"id":861,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\websocket.js","layer":"pages-dir-browser"},"startTime":1750489200421,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83503,"timestamp":287145945176,"id":863,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\pages-dev-overlay.js","layer":"pages-dir-browser"},"startTime":1750489200422,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":90959,"timestamp":287145945401,"id":864,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\client.js","layer":"pages-dir-browser"},"startTime":1750489200422,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":94697,"timestamp":287145945953,"id":867,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\tracing\\report-to-socket.js","layer":"pages-dir-browser"},"startTime":1750489200422,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":111195,"timestamp":287145944938,"id":862,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\hot-reloader-client.js","layer":"pages-dir-browser"},"startTime":1750489200421,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":115032,"timestamp":287145945649,"id":865,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\on-recoverable-error.js","layer":"pages-dir-browser"},"startTime":1750489200422,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":120933,"timestamp":287145945810,"id":866,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\tracing\\tracer.js","layer":"pages-dir-browser"},"startTime":1750489200422,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":122932,"timestamp":287145946253,"id":869,"parentId":830,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-plain-object.js","layer":"pages-dir-browser"},"startTime":1750489200423,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":126504,"timestamp":287145946449,"id":870,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\side-effect.js","layer":"pages-dir-browser"},"startTime":1750489200423,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":136825,"timestamp":287145946910,"id":872,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\amp-mode.js","layer":"pages-dir-browser"},"startTime":1750489200423,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":132983,"timestamp":287145952801,"id":875,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils\\warn-once.js","layer":"pages-dir-browser"},"startTime":1750489200429,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":141853,"timestamp":287145946623,"id":871,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\amp-context.shared-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200423,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":115156,"timestamp":287145984311,"id":878,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\detect-domain-locale.js","layer":"pages-dir-browser"},"startTime":1750489200461,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":117030,"timestamp":287145985135,"id":881,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\add-base-path.js","layer":"pages-dir-browser"},"startTime":1750489200462,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":121435,"timestamp":287145984846,"id":880,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\remove-locale.js","layer":"pages-dir-browser"},"startTime":1750489200461,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":124948,"timestamp":287145984592,"id":879,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\add-locale.js","layer":"pages-dir-browser"},"startTime":1750489200461,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":167206,"timestamp":287145983924,"id":877,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\route-loader.js","layer":"pages-dir-browser"},"startTime":1750489200460,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":169557,"timestamp":287145986520,"id":884,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\bloom-filter.js","layer":"pages-dir-browser"},"startTime":1750489200463,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":175612,"timestamp":287145985980,"id":883,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\resolve-href.js","layer":"pages-dir-browser"},"startTime":1750489200462,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":177956,"timestamp":287145987114,"id":887,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\remove-trailing-slash.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":181081,"timestamp":287145987246,"id":888,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\route-matcher.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":183672,"timestamp":287145987683,"id":891,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\get-next-pathname-info.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":192745,"timestamp":287145987371,"id":889,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\route-regex.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":195898,"timestamp":287145987516,"id":890,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\format-url.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":197201,"timestamp":287145987804,"id":892,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\parse-path.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":200374,"timestamp":287145986953,"id":886,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\parse-relative-url.js","layer":"pages-dir-browser"},"startTime":1750489200463,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":201713,"timestamp":287145988017,"id":894,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\compare-states.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":205904,"timestamp":287145987910,"id":893,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\format-next-pathname-info.js","layer":"pages-dir-browser"},"startTime":1750489200464,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":209719,"timestamp":287145988169,"id":895,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-local-url.js","layer":"pages-dir-browser"},"startTime":1750489200465,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":212558,"timestamp":287145988406,"id":897,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\omit.js","layer":"pages-dir-browser"},"startTime":1750489200465,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":215337,"timestamp":287145988494,"id":898,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interpolate-as.js","layer":"pages-dir-browser"},"startTime":1750489200465,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":217933,"timestamp":287145988262,"id":896,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-bot.js","layer":"pages-dir-browser"},"startTime":1750489200465,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":295935,"timestamp":287145946075,"id":868,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\polyfills\\polyfill-module.js","layer":"pages-dir-browser"},"startTime":1750489200422,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":295337,"timestamp":287145949327,"id":873,"parentId":826,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\dev\\hot-reloader-types.js","layer":"pages-dir-browser"},"startTime":1750489200426,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":315637,"timestamp":287145983559,"id":876,"parentId":819,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\jsx-runtime.js","layer":"pages-dir-browser"},"startTime":1750489200460,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":315001,"timestamp":287145985424,"id":882,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-api-route.js","layer":"pages-dir-browser"},"startTime":1750489200462,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":317493,"timestamp":287145986761,"id":885,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\constants.js","layer":"pages-dir-browser"},"startTime":1750489200463,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30151,"timestamp":287146282806,"id":902,"parentId":849,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\image-config.js","layer":"pages-dir-browser"},"startTime":1750489200759,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35051,"timestamp":287146281455,"id":899,"parentId":839,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\set-attributes-from-props.js","layer":"pages-dir-browser"},"startTime":1750489200758,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36900,"timestamp":287146282205,"id":900,"parentId":841,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\request-idle-callback.js","layer":"pages-dir-browser"},"startTime":1750489200759,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37663,"timestamp":287146283214,"id":905,"parentId":852,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\as-path-to-search-params.js","layer":"pages-dir-browser"},"startTime":1750489200760,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39591,"timestamp":287146283103,"id":904,"parentId":843,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\path-has-prefix.js","layer":"pages-dir-browser"},"startTime":1750489200760,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":44183,"timestamp":287146282964,"id":903,"parentId":838,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\get-asset-path-from-route.js","layer":"pages-dir-browser"},"startTime":1750489200759,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66983,"timestamp":287146283920,"id":909,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\i18n\\normalize-locale-path.js","layer":"pages-dir-browser"},"startTime":1750489200760,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":70723,"timestamp":287146283368,"id":906,"parentId":855,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interception-routes.js","layer":"pages-dir-browser"},"startTime":1750489200760,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":77915,"timestamp":287146282610,"id":901,"parentId":838,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\constants.js","layer":"pages-dir-browser"},"startTime":1750489200759,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":79634,"timestamp":287146283689,"id":908,"parentId":833,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\denormalize-page-path.js","layer":"pages-dir-browser"},"startTime":1750489200760,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":86490,"timestamp":287146284205,"id":911,"parentId":856,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\handle-dev-build-indicator-hmr-events.js","layer":"pages-dir-browser"},"startTime":1750489200761,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":88975,"timestamp":287146284085,"id":910,"parentId":857,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\dev\\dev-build-indicator\\internal\\initialize.js","layer":"pages-dir-browser"},"startTime":1750489200761,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":56423,"timestamp":287146329314,"id":912,"parentId":858,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-error.js","layer":"pages-dir-browser"},"startTime":1750489200806,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30180,"timestamp":287146395215,"id":914,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\portal\\index.js","layer":"pages-dir-browser"},"startTime":1750489200872,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33295,"timestamp":287146395786,"id":915,"parentId":863,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\pages-dev-overlay-error-boundary.js","layer":"pages-dir-browser"},"startTime":1750489200872,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37882,"timestamp":287146396362,"id":916,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\attach-hydration-error-state.js","layer":"pages-dir-browser"},"startTime":1750489200873,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":58426,"timestamp":287146396980,"id":919,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\bus.js","layer":"pages-dir-browser"},"startTime":1750489200873,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62055,"timestamp":287146396863,"id":918,"parentId":863,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\pages\\hooks.js","layer":"pages-dir-browser"},"startTime":1750489200873,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":67949,"timestamp":287146396700,"id":917,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\hydration-error-info.js","layer":"pages-dir-browser"},"startTime":1750489200873,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":184196,"timestamp":287146283514,"id":907,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\client.js","layer":"pages-dir-browser"},"startTime":1750489200760,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30362,"timestamp":287146440405,"id":924,"parentId":877,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\encode-uri-path.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33177,"timestamp":287146440095,"id":922,"parentId":877,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\trusted-types.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37372,"timestamp":287146440268,"id":923,"parentId":881,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\normalize-trailing-slash.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40321,"timestamp":287146439845,"id":921,"parentId":858,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\http-access-fallback.js","layer":"pages-dir-browser"},"startTime":1750489200916,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":58105,"timestamp":287146440789,"id":927,"parentId":881,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-path-prefix.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":61678,"timestamp":287146440507,"id":925,"parentId":865,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\react-client-callbacks\\report-global-error.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":65749,"timestamp":287146440924,"id":928,"parentId":889,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\escape-regexp.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":71780,"timestamp":287146440654,"id":926,"parentId":865,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\stitched-error.js","layer":"pages-dir-browser"},"startTime":1750489200917,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":73037,"timestamp":287146442310,"id":929,"parentId":891,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\remove-path-prefix.js","layer":"pages-dir-browser"},"startTime":1750489200919,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":202076,"timestamp":287146329917,"id":913,"parentId":820,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_interop_require_default.js","layer":"pages-dir-browser"},"startTime":1750489200806,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":54482,"timestamp":287146485914,"id":931,"parentId":861,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\get-socket-url.js","layer":"pages-dir-browser"},"startTime":1750489200962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":59935,"timestamp":287146486217,"id":932,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\parse-stack.js","layer":"pages-dir-browser"},"startTime":1750489200963,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":82802,"timestamp":287146486640,"id":934,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\get-error-by-type.js","layer":"pages-dir-browser"},"startTime":1750489200963,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":88610,"timestamp":287146486426,"id":933,"parentId":862,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\format-webpack-messages.js","layer":"pages-dir-browser"},"startTime":1750489200963,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":94454,"timestamp":287146486809,"id":935,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\node-stack-frames.js","layer":"pages-dir-browser"},"startTime":1750489200963,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":96608,"timestamp":287146487099,"id":937,"parentId":862,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\report-hmr-latency.js","layer":"pages-dir-browser"},"startTime":1750489200964,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":100778,"timestamp":287146486948,"id":936,"parentId":864,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\parse-component-stack.js","layer":"pages-dir-browser"},"startTime":1750489200963,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":104773,"timestamp":287146487392,"id":939,"parentId":863,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\font\\font-styles.js","layer":"pages-dir-browser"},"startTime":1750489200964,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":108847,"timestamp":287146487636,"id":940,"parentId":863,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\dev-overlay.js","layer":"pages-dir-browser"},"startTime":1750489200964,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":116340,"timestamp":287146487240,"id":938,"parentId":862,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\turbopack-hot-reloader-common.js","layer":"pages-dir-browser"},"startTime":1750489200964,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":56736,"timestamp":287146550172,"id":943,"parentId":896,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\html-bots.js","layer":"pages-dir-browser"},"startTime":1750489201027,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":61055,"timestamp":287146550426,"id":944,"parentId":865,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\bailout-to-csr.js","layer":"pages-dir-browser"},"startTime":1750489201027,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":69295,"timestamp":287146549340,"id":942,"parentId":893,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-locale.js","layer":"pages-dir-browser"},"startTime":1750489201026,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":72862,"timestamp":287146548568,"id":941,"parentId":893,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\add-path-suffix.js","layer":"pages-dir-browser"},"startTime":1750489201025,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":207311,"timestamp":287146439596,"id":920,"parentId":819,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\index.js","layer":"pages-dir-browser"},"startTime":1750489200916,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":21207,"timestamp":287146628515,"id":948,"parentId":901,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\modern-browserslist-target.js","layer":"pages-dir-browser"},"startTime":1750489201105,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26509,"timestamp":287146628996,"id":949,"parentId":912,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\redirect-status-code.js","layer":"pages-dir-browser"},"startTime":1750489201105,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36872,"timestamp":287146626784,"id":946,"parentId":852,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\index.js","layer":"pages-dir-browser"},"startTime":1750489201103,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40906,"timestamp":287146629352,"id":950,"parentId":908,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\normalize-path-sep.js","layer":"pages-dir-browser"},"startTime":1750489201106,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":49209,"timestamp":287146629582,"id":951,"parentId":906,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\app-paths.js","layer":"pages-dir-browser"},"startTime":1750489201106,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":202138,"timestamp":287146485614,"id":930,"parentId":828,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-is\\index.js","layer":"pages-dir-browser"},"startTime":1750489200962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":97038,"timestamp":287146625837,"id":945,"parentId":831,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_interop_require_wildcard.js","layer":"pages-dir-browser"},"startTime":1750489201102,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":101243,"timestamp":287146627923,"id":947,"parentId":876,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\cjs\\react-jsx-runtime.development.js","layer":"pages-dir-browser"},"startTime":1750489201104,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34520,"timestamp":287146701454,"id":952,"parentId":916,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\is-hydration-error.js","layer":"pages-dir-browser"},"startTime":1750489201178,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":38245,"timestamp":287146717310,"id":954,"parentId":931,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\normalized-asset-prefix.js","layer":"pages-dir-browser"},"startTime":1750489201194,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":23866,"timestamp":287146741399,"id":956,"parentId":934,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\error-source.js","layer":"pages-dir-browser"},"startTime":1750489201218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26208,"timestamp":287146742001,"id":958,"parentId":939,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\css.js","layer":"pages-dir-browser"},"startTime":1750489201218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":31783,"timestamp":287146741776,"id":957,"parentId":934,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\stack-frame.js","layer":"pages-dir-browser"},"startTime":1750489201218,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":79812,"timestamp":287146716830,"id":953,"parentId":926,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\error-telemetry-utils.js","layer":"pages-dir-browser"},"startTime":1750489201193,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":23267,"timestamp":287146776478,"id":961,"parentId":951,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\ensure-leading-slash.js","layer":"pages-dir-browser"},"startTime":1750489201253,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":28596,"timestamp":287146775663,"id":960,"parentId":951,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\segment.js","layer":"pages-dir-browser"},"startTime":1750489201252,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39914,"timestamp":287146777107,"id":962,"parentId":946,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\sorted-routes.js","layer":"pages-dir-browser"},"startTime":1750489201254,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35761,"timestamp":287146784286,"id":965,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\base.js","layer":"pages-dir-browser"},"startTime":1750489201261,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":38102,"timestamp":287146784567,"id":966,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\colors.js","layer":"pages-dir-browser"},"startTime":1750489201261,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":41973,"timestamp":287146783840,"id":964,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\shadow-portal.js","layer":"pages-dir-browser"},"startTime":1750489201260,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":46345,"timestamp":287146785002,"id":968,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\component-styles.js","layer":"pages-dir-browser"},"startTime":1750489201261,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":48934,"timestamp":287146785166,"id":969,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\dark-theme.js","layer":"pages-dir-browser"},"startTime":1750489201262,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":51812,"timestamp":287146784808,"id":967,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\styles\\css-reset.js","layer":"pages-dir-browser"},"startTime":1750489201261,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":114096,"timestamp":287146742193,"id":959,"parentId":920,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react\\cjs\\react.development.js","layer":"pages-dir-browser"},"startTime":1750489201219,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":442725,"timestamp":287146740805,"id":955,"parentId":907,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\cjs\\react-dom-client.development.js","layer":"pages-dir-browser"},"startTime":1750489201217,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":323360,"timestamp":287146862787,"id":971,"parentId":862,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\strip-ansi\\index.js","layer":"pages-dir-browser"},"startTime":1750489201339,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":324896,"timestamp":287146864164,"id":974,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\preferences.js","layer":"pages-dir-browser"},"startTime":1750489201341,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":342852,"timestamp":287146863900,"id":973,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay\\error-overlay.js","layer":"pages-dir-browser"},"startTime":1750489201340,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":352738,"timestamp":287146864349,"id":975,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\render-error.js","layer":"pages-dir-browser"},"startTime":1750489201341,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":366344,"timestamp":287146863516,"id":972,"parentId":940,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-indicator.js","layer":"pages-dir-browser"},"startTime":1750489201340,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":484545,"timestamp":287146783464,"id":963,"parentId":841,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\index.js","layer":"pages-dir-browser"},"startTime":1750489201260,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":41490,"timestamp":287147234721,"id":976,"parentId":957,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\webpack-module-path.js","layer":"pages-dir-browser"},"startTime":1750489201711,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":477836,"timestamp":287146805267,"id":970,"parentId":930,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\react-is\\cjs\\react-is.development.js","layer":"pages-dir-browser"},"startTime":1750489201282,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":69009,"timestamp":287147321223,"id":977,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\build-error.js","layer":"pages-dir-browser"},"startTime":1750489201798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":77595,"timestamp":287147321845,"id":979,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\dev-tools-info.js","layer":"pages-dir-browser"},"startTime":1750489201798,"traceId":"901c6e83d477b378"}]
[{"name":"build-module-js","duration":91612,"timestamp":287147321615,"id":978,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\errors.js","layer":"pages-dir-browser"},"startTime":1750489201798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":116064,"timestamp":287147322028,"id":980,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\turbopack-info.js","layer":"pages-dir-browser"},"startTime":1750489201798,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":119436,"timestamp":287147322200,"id":981,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\route-info.js","layer":"pages-dir-browser"},"startTime":1750489201799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":133538,"timestamp":287147322362,"id":982,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\dev-tools-info\\user-preferences.js","layer":"pages-dir-browser"},"startTime":1750489201799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":123208,"timestamp":287147339234,"id":984,"parentId":972,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\utils.js","layer":"pages-dir-browser"},"startTime":1750489201816,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":130045,"timestamp":287147338847,"id":983,"parentId":972,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\next-logo.js","layer":"pages-dir-browser"},"startTime":1750489201815,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":773421,"timestamp":287147484871,"id":989,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\styles.js","layer":"pages-dir-browser"},"startTime":1750489201961,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":786162,"timestamp":287147481388,"id":986,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-layout\\error-overlay-layout.js","layer":"pages-dir-browser"},"startTime":1750489201958,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":795081,"timestamp":287147485080,"id":990,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-footer\\error-overlay-footer.js","layer":"pages-dir-browser"},"startTime":1750489201962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":812335,"timestamp":287147484529,"id":988,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-pagination\\error-overlay-pagination.js","layer":"pages-dir-browser"},"startTime":1750489201961,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":823141,"timestamp":287147485352,"id":992,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\editor-link.js","layer":"pages-dir-browser"},"startTime":1750489201962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":842108,"timestamp":287147485228,"id":991,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\terminal.js","layer":"pages-dir-browser"},"startTime":1750489201962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":846053,"timestamp":287147485468,"id":993,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\version-staleness-info\\version-staleness-info.js","layer":"pages-dir-browser"},"startTime":1750489201962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":853684,"timestamp":287147483673,"id":987,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\code-frame\\code-frame.js","layer":"pages-dir-browser"},"startTime":1750489201960,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":865273,"timestamp":287147485597,"id":994,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\call-stack-frame\\call-stack-frame.js","layer":"pages-dir-browser"},"startTime":1750489201962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":867328,"timestamp":287147485712,"id":995,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\environment-name-label\\environment-name-label.js","layer":"pages-dir-browser"},"startTime":1750489201962,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36992,"timestamp":287148385818,"id":1001,"parentId":972,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\dev-indicator\\dev-render-indicator.js","layer":"pages-dir-browser"},"startTime":1750489202862,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":38813,"timestamp":287148386221,"id":1002,"parentId":972,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\gear-icon.js","layer":"pages-dir-browser"},"startTime":1750489202863,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":43774,"timestamp":287148383710,"id":1000,"parentId":973,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-delayed-render.js","layer":"pages-dir-browser"},"startTime":1750489202860,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":1096670,"timestamp":287147339427,"id":985,"parentId":963,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\react-dom\\cjs\\react-dom.development.js","layer":"pages-dir-browser"},"startTime":1750489201816,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":81469,"timestamp":287148383455,"id":999,"parentId":938,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_class_private_field_loose_key.js","layer":"pages-dir-browser"},"startTime":1750489202860,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":82624,"timestamp":287148382904,"id":997,"parentId":939,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_tagged_template_literal_loose.js","layer":"pages-dir-browser"},"startTime":1750489202859,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":83500,"timestamp":287148382429,"id":996,"parentId":938,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\esm\\_class_private_field_loose_base.js","layer":"pages-dir-browser"},"startTime":1750489202859,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":84161,"timestamp":287148383205,"id":998,"parentId":932,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\stacktrace-parser\\stack-trace-parser.cjs.js","layer":"pages-dir-browser"},"startTime":1750489202860,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":14899,"timestamp":287148456507,"id":1005,"parentId":982,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\dark-icon.js","layer":"pages-dir-browser"},"startTime":1750489202933,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":16872,"timestamp":287148456215,"id":1004,"parentId":978,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\component-stack-pseudo-html.js","layer":"pages-dir-browser"},"startTime":1750489202933,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":19019,"timestamp":287148455695,"id":1003,"parentId":978,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\errors\\console-error.js","layer":"pages-dir-browser"},"startTime":1750489202932,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":20158,"timestamp":287148456727,"id":1006,"parentId":982,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\light-icon.js","layer":"pages-dir-browser"},"startTime":1750489202933,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":22811,"timestamp":287148457068,"id":1008,"parentId":982,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\system-icon.js","layer":"pages-dir-browser"},"startTime":1750489202933,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":24613,"timestamp":287148456921,"id":1007,"parentId":982,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\eye-icon.js","layer":"pages-dir-browser"},"startTime":1750489202933,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26510,"timestamp":287148457197,"id":1009,"parentId":983,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dev-tools-indicator\\use-minimum-loading-time-multiple.js","layer":"pages-dir-browser"},"startTime":1750489202934,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35379,"timestamp":287148468674,"id":1013,"parentId":988,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\right-arrow.js","layer":"pages-dir-browser"},"startTime":1750489202945,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":37551,"timestamp":287148468572,"id":1012,"parentId":988,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\left-arrow.js","layer":"pages-dir-browser"},"startTime":1750489202945,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40729,"timestamp":287148468196,"id":1010,"parentId":991,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\external.js","layer":"pages-dir-browser"},"startTime":1750489202945,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":43188,"timestamp":287148468455,"id":1011,"parentId":991,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\file.js","layer":"pages-dir-browser"},"startTime":1750489202945,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":46385,"timestamp":287148468765,"id":1014,"parentId":987,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\code-frame\\parse-code-frame.js","layer":"pages-dir-browser"},"startTime":1750489202945,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39794,"timestamp":287148497370,"id":1018,"parentId":993,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\cx.js","layer":"pages-dir-browser"},"startTime":1750489202974,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":43077,"timestamp":287148496953,"id":1016,"parentId":983,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\merge-refs.js","layer":"pages-dir-browser"},"startTime":1750489202973,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":48655,"timestamp":287148496325,"id":1015,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\container\\runtime-error\\index.js","layer":"pages-dir-browser"},"startTime":1750489202973,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":51828,"timestamp":287148497209,"id":1017,"parentId":992,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\use-open-in-editor.js","layer":"pages-dir-browser"},"startTime":1750489202974,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":25512,"timestamp":287148527380,"id":1022,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\header.js","layer":"pages-dir-browser"},"startTime":1750489203004,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":27857,"timestamp":287148527184,"id":1021,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\body.js","layer":"pages-dir-browser"},"startTime":1750489203004,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":31254,"timestamp":287148526827,"id":1020,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\dialog\\dialog.js","layer":"pages-dir-browser"},"startTime":1750489203003,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34488,"timestamp":287148526171,"id":1019,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-message\\error-message.js","layer":"pages-dir-browser"},"startTime":1750489203003,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":55597,"timestamp":287148527954,"id":1025,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-type-label\\error-type-label.js","layer":"pages-dir-browser"},"startTime":1750489203004,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":59025,"timestamp":287148527561,"id":1023,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\error-overlay-toolbar.js","layer":"pages-dir-browser"},"startTime":1750489203004,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62518,"timestamp":287148527727,"id":1024,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-nav\\error-overlay-nav.js","layer":"pages-dir-browser"},"startTime":1750489203004,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":65894,"timestamp":287148528399,"id":1028,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\overlay\\overlay.js","layer":"pages-dir-browser"},"startTime":1750489203005,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":70990,"timestamp":287148528135,"id":1026,"parentId":986,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\call-stack\\call-stack.js","layer":"pages-dir-browser"},"startTime":1750489203005,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":76601,"timestamp":287148528275,"id":1027,"parentId":990,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-footer\\error-feedback\\error-feedback.js","layer":"pages-dir-browser"},"startTime":1750489203005,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":44815,"timestamp":287148564772,"id":1033,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\index.js","layer":"pages-dir-browser"},"startTime":1750489203041,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":47841,"timestamp":287148564031,"id":1030,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\fader\\index.js","layer":"pages-dir-browser"},"startTime":1750489203040,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":55681,"timestamp":287148564570,"id":1032,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-bottom-stack\\index.js","layer":"pages-dir-browser"},"startTime":1750489203041,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":57245,"timestamp":287148564897,"id":1034,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\index.js","layer":"pages-dir-browser"},"startTime":1750489203041,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":79320,"timestamp":287148564365,"id":1031,"parentId":968,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\copy-button\\index.js","layer":"pages-dir-browser"},"startTime":1750489203041,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":134673,"timestamp":287148563749,"id":1029,"parentId":955,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\polyfills\\process.js","layer":"pages-dir-browser"},"startTime":1750489203040,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15864,"timestamp":287148706566,"id":1035,"parentId":977,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\terminal\\index.js","layer":"pages-dir-browser"},"startTime":1750489203183,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":18460,"timestamp":287148706957,"id":1036,"parentId":978,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\index.js","layer":"pages-dir-browser"},"startTime":1750489203183,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":21389,"timestamp":287148707256,"id":1037,"parentId":1022,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-header.js","layer":"pages-dir-browser"},"startTime":1750489203184,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":27736,"timestamp":287148707757,"id":1038,"parentId":1020,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog.js","layer":"pages-dir-browser"},"startTime":1750489203184,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":31238,"timestamp":287148716097,"id":1041,"parentId":1033,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-content.js","layer":"pages-dir-browser"},"startTime":1750489203193,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":32617,"timestamp":287148716534,"id":1042,"parentId":1033,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\styles.js","layer":"pages-dir-browser"},"startTime":1750489203193,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35496,"timestamp":287148715825,"id":1040,"parentId":1033,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\dialog\\dialog-body.js","layer":"pages-dir-browser"},"startTime":1750489203192,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36070,"timestamp":287148716714,"id":1043,"parentId":1034,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\styles.js","layer":"pages-dir-browser"},"startTime":1750489203193,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40116,"timestamp":287148715379,"id":1039,"parentId":1004,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\hydration-diff\\diff-view.js","layer":"pages-dir-browser"},"startTime":1750489203192,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40783,"timestamp":287148716853,"id":1044,"parentId":1034,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\toast\\toast.js","layer":"pages-dir-browser"},"startTime":1750489203193,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":42811,"timestamp":287148716975,"id":1045,"parentId":1028,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\overlay.js","layer":"pages-dir-browser"},"startTime":1750489203193,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":44496,"timestamp":287148717101,"id":1046,"parentId":1023,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\copy-stack-trace-button.js","layer":"pages-dir-browser"},"startTime":1750489203194,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":46946,"timestamp":287148717391,"id":1048,"parentId":1023,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\docs-link-button.js","layer":"pages-dir-browser"},"startTime":1750489203194,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":52280,"timestamp":287148717246,"id":1047,"parentId":1023,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\errors\\error-overlay-toolbar\\nodejs-inspector-button.js","layer":"pages-dir-browser"},"startTime":1750489203194,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":41267,"timestamp":287148738524,"id":1049,"parentId":978,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\hot-linked-text\\index.js","layer":"pages-dir-browser"},"startTime":1750489203215,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33239,"timestamp":287148794279,"id":1052,"parentId":1027,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\thumbs\\thumbs-down.js","layer":"pages-dir-browser"},"startTime":1750489203271,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":36594,"timestamp":287148793724,"id":1051,"parentId":1027,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\thumbs\\thumbs-up.js","layer":"pages-dir-browser"},"startTime":1750489203270,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":52576,"timestamp":287148793234,"id":1050,"parentId":955,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\scheduler\\index.js","layer":"pages-dir-browser"},"startTime":1750489203270,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":10329,"timestamp":287148842859,"id":1054,"parentId":1038,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-measure-height.js","layer":"pages-dir-browser"},"startTime":1750489203319,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":12058,"timestamp":287148843367,"id":1055,"parentId":1038,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\hooks\\use-on-click-outside.js","layer":"pages-dir-browser"},"startTime":1750489203320,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15363,"timestamp":287148846835,"id":1056,"parentId":1039,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\icons\\collapse-icon.js","layer":"pages-dir-browser"},"startTime":1750489203323,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33566,"timestamp":287148835260,"id":1053,"parentId":991,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\anser\\index.js","layer":"pages-dir-browser"},"startTime":1750489203312,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":23130,"timestamp":287148847795,"id":1059,"parentId":1048,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\utils\\parse-url-from-text.js","layer":"pages-dir-browser"},"startTime":1750489203324,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":27043,"timestamp":287148847331,"id":1057,"parentId":1045,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\ui\\components\\overlay\\body-locker.js","layer":"pages-dir-browser"},"startTime":1750489203324,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":30622,"timestamp":287148847587,"id":1058,"parentId":1049,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\magic-identifier.js","layer":"pages-dir-browser"},"startTime":1750489203324,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":8644,"timestamp":287148884464,"id":1060,"parentId":1050,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\scheduler\\cjs\\scheduler.development.js","layer":"pages-dir-browser"},"startTime":1750489203361,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":8628,"timestamp":287148887533,"id":1061,"parentId":1029,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\process\\browser.js","layer":"pages-dir-browser"},"startTime":1750489203364,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3788992,"timestamp":287145107454,"id":805,"parentId":799,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\router.js"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3788942,"timestamp":287145107706,"id":810,"parentId":799,"tags":{"request":"next-client-pages-loader?absolutePagePath=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":3789404,"timestamp":287145107263,"id":801,"parentId":799,"tags":{"request":"./node_modules/next/dist/client/next-dev.js"},"startTime":1750489199584,"traceId":"901c6e83d477b378"},{"name":"make","duration":3807866,"timestamp":287145088868,"id":799,"parentId":798,"tags":{},"startTime":1750489199565,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":28307,"timestamp":287148927623,"id":1063,"parentId":1062,"tags":{},"startTime":1750489203404,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":35,"timestamp":287148956107,"id":1065,"parentId":1062,"tags":{},"startTime":1750489203433,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":370,"timestamp":287148956202,"id":1066,"parentId":1062,"tags":{},"startTime":1750489203433,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":40,"timestamp":287148956660,"id":1067,"parentId":1062,"tags":{},"startTime":1750489203433,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":31,"timestamp":287148956759,"id":1068,"parentId":1062,"tags":{},"startTime":1750489203433,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":7607,"timestamp":287148956056,"id":1064,"parentId":1062,"tags":{},"startTime":1750489203432,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":28849,"timestamp":287148974583,"id":1069,"parentId":1062,"tags":{},"startTime":1750489203451,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":69449,"timestamp":287149003538,"id":1070,"parentId":1062,"tags":{},"startTime":1750489203480,"traceId":"901c6e83d477b378"},{"name":"hash","duration":28782,"timestamp":287149086968,"id":1071,"parentId":1062,"tags":{},"startTime":1750489203563,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":680,"timestamp":287149115732,"id":1072,"parentId":1062,"tags":{},"startTime":1750489203592,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":580,"timestamp":287149116260,"id":1073,"parentId":1062,"tags":{},"startTime":1750489203593,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":297298,"timestamp":287149116887,"id":1074,"parentId":1062,"tags":{},"startTime":1750489203593,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1043,"timestamp":287149420744,"id":1076,"parentId":798,"tags":{},"startTime":1750489203897,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":1751,"timestamp":287149420129,"id":1075,"parentId":798,"tags":{},"startTime":1750489203897,"traceId":"901c6e83d477b378"}]
[{"name":"seal","duration":571316,"timestamp":287148909786,"id":1062,"parentId":798,"tags":{},"startTime":1750489203386,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":4393770,"timestamp":287145087514,"id":798,"parentId":795,"tags":{"name":"client"},"startTime":1750489199564,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-client","duration":4407669,"timestamp":287145077719,"id":795,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489199554,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":95250,"timestamp":287149562168,"id":1084,"parentId":1078,"tags":{"request":"next-app-loader?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489204039,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":44321,"timestamp":287149641235,"id":1085,"parentId":1079,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_app.js","layer":"pages-dir-node"},"startTime":1750489204118,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":1855,"timestamp":287149696013,"id":1086,"parentId":1085,"tags":{"name":"react/jsx-runtime","layer":null},"startTime":1750489204172,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":66,"timestamp":287149697959,"id":1087,"parentId":1085,"tags":{"name":"react","layer":null},"startTime":1750489204174,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":143009,"timestamp":287149562145,"id":1082,"parentId":1078,"tags":{"request":"next-app-loader?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489204039,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":13132,"timestamp":287149698054,"id":1088,"parentId":1085,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils.js","layer":"pages-dir-node"},"startTime":1750489204174,"traceId":"901c6e83d477b378"},{"name":"build-module-cjs","duration":14563,"timestamp":287149733272,"id":1091,"parentId":1085,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\cjs\\_interop_require_default.cjs","layer":"pages-dir-node"},"startTime":1750489204210,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":186103,"timestamp":287149561891,"id":1079,"parentId":1078,"tags":{"request":"private-next-pages/_app"},"startTime":1750489204038,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":59547,"timestamp":287149711589,"id":1089,"parentId":1080,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-route-loader\\index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!","layer":"pages-dir-node"},"startTime":1750489204188,"traceId":"901c6e83d477b378"},{"name":"build-module","duration":59559,"timestamp":287149713894,"id":1090,"parentId":1083,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-route-loader\\index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!","layer":null},"startTime":1750489204190,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":11086,"timestamp":287149789945,"id":1092,"parentId":1090,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_app.js","layer":null},"startTime":1750489204266,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":13715,"timestamp":287149792117,"id":1093,"parentId":1090,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_error.js","layer":null},"startTime":1750489204269,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":27505,"timestamp":287149811707,"id":1094,"parentId":1089,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_error.js","layer":"pages-dir-node"},"startTime":1750489204288,"traceId":"901c6e83d477b378"},{"name":"build-module-cjs","duration":3076,"timestamp":287149839998,"id":1097,"parentId":1092,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\cjs\\_interop_require_default.cjs","layer":null},"startTime":1750489204316,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40708,"timestamp":287149814310,"id":1095,"parentId":1089,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-kind.js","layer":"pages-dir-node"},"startTime":1750489204291,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39368,"timestamp":287149816672,"id":1096,"parentId":1090,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-kind.js","layer":null},"startTime":1750489204293,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":23186,"timestamp":287149840255,"id":1098,"parentId":1092,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils.js","layer":null},"startTime":1750489204317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":32991,"timestamp":287149840646,"id":1099,"parentId":1093,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head.js","layer":null},"startTime":1750489204317,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":22176,"timestamp":287149856347,"id":1102,"parentId":1094,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\head.js","layer":"pages-dir-node"},"startTime":1750489204333,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":53048,"timestamp":287149841631,"id":1100,"parentId":1089,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\templates\\helpers.js","layer":"pages-dir-node"},"startTime":1750489204318,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":53379,"timestamp":287149842071,"id":1101,"parentId":1090,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\build\\templates\\helpers.js","layer":null},"startTime":1750489204319,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":49385,"timestamp":287149863665,"id":1103,"parentId":1093,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\request-meta.js","layer":null},"startTime":1750489204340,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":49082,"timestamp":287149865360,"id":1104,"parentId":1094,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\request-meta.js","layer":"pages-dir-node"},"startTime":1750489204342,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15048,"timestamp":287149907714,"id":1110,"parentId":1102,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\side-effect.js","layer":"pages-dir-node"},"startTime":1750489204384,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":22601,"timestamp":287149906650,"id":1109,"parentId":1099,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\side-effect.js","layer":null},"startTime":1750489204383,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":23669,"timestamp":287149908391,"id":1112,"parentId":1102,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\amp-mode.js","layer":"pages-dir-node"},"startTime":1750489204385,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":24921,"timestamp":287149908181,"id":1111,"parentId":1099,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\amp-mode.js","layer":null},"startTime":1750489204385,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":28471,"timestamp":287149908585,"id":1113,"parentId":1099,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils\\warn-once.js","layer":null},"startTime":1750489204385,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":70829,"timestamp":287149867776,"id":1105,"parentId":1089,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\module.compiled.js","layer":"pages-dir-node"},"startTime":1750489204344,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":73713,"timestamp":287149868153,"id":1106,"parentId":1090,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\module.compiled.js","layer":null},"startTime":1750489204345,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34331,"timestamp":287149908704,"id":1114,"parentId":1102,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\utils\\warn-once.js","layer":"pages-dir-node"},"startTime":1750489204385,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":107253,"timestamp":287149879569,"id":1107,"parentId":1089,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_document.js","layer":"pages-dir-node"},"startTime":1750489204356,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":141249,"timestamp":287149879850,"id":1108,"parentId":1090,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\pages\\_document.js","layer":null},"startTime":1750489204356,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":383,"timestamp":287150098628,"id":1117,"parentId":1105,"tags":{"name":"next/dist/compiled/next-server/pages.runtime.dev.js","layer":null},"startTime":1750489204575,"traceId":"901c6e83d477b378"},{"name":"build-module-cjs","duration":190527,"timestamp":287149919066,"id":1115,"parentId":1099,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\cjs\\_interop_require_wildcard.cjs","layer":null},"startTime":1750489204396,"traceId":"901c6e83d477b378"},{"name":"build-module-cjs","duration":196154,"timestamp":287149919997,"id":1116,"parentId":1102,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\@swc\\helpers\\cjs\\_interop_require_wildcard.cjs","layer":"pages-dir-node"},"startTime":1750489204396,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":6679,"timestamp":287150119442,"id":1118,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-error.js","layer":"pages-dir-node"},"startTime":1750489204596,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":18178,"timestamp":287150119753,"id":1119,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\is-error.js","layer":null},"startTime":1750489204596,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":46712,"timestamp":287150120690,"id":1123,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\encode-uri-path.js","layer":null},"startTime":1750489204597,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":51654,"timestamp":287150120190,"id":1122,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\encode-uri-path.js","layer":"pages-dir-node"},"startTime":1750489204597,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":55989,"timestamp":287150120047,"id":1121,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\constants.js","layer":null},"startTime":1750489204596,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66450,"timestamp":287150119850,"id":1120,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\constants.js","layer":"pages-dir-node"},"startTime":1750489204596,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":13476,"timestamp":287150194776,"id":1132,"parentId":1118,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-plain-object.js","layer":"pages-dir-node"},"startTime":1750489204671,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15053,"timestamp":287150195891,"id":1133,"parentId":1119,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-plain-object.js","layer":null},"startTime":1750489204672,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":13380,"timestamp":287150200331,"id":1138,"parentId":1121,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\modern-browserslist-target.js","layer":null},"startTime":1750489204677,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":13846,"timestamp":287150200755,"id":1139,"parentId":1120,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\modern-browserslist-target.js","layer":"pages-dir-node"},"startTime":1750489204677,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":28649,"timestamp":287150187732,"id":1126,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\pretty-bytes.js","layer":"pages-dir-node"},"startTime":1750489204664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":29996,"timestamp":287150187850,"id":1127,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\lib\\pretty-bytes.js","layer":null},"startTime":1750489204664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":31334,"timestamp":287150187920,"id":1128,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\htmlescape.js","layer":"pages-dir-node"},"startTime":1750489204664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":33028,"timestamp":287150188052,"id":1129,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\htmlescape.js","layer":null},"startTime":1750489204664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35035,"timestamp":287150187329,"id":1124,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\get-page-files.js","layer":"pages-dir-node"},"startTime":1750489204664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":35916,"timestamp":287150187615,"id":1125,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\get-page-files.js","layer":null},"startTime":1750489204664,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39071,"timestamp":287150188109,"id":1130,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\utils.js","layer":"pages-dir-node"},"startTime":1750489204665,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":41423,"timestamp":287150188196,"id":1131,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\utils.js","layer":null},"startTime":1750489204665,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39257,"timestamp":287150196110,"id":1134,"parentId":1099,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\vendored\\contexts\\amp-context.js","layer":null},"startTime":1750489204673,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39526,"timestamp":287150196346,"id":1135,"parentId":1102,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\vendored\\contexts\\amp-context.js","layer":"pages-dir-node"},"startTime":1750489204673,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39938,"timestamp":287150196475,"id":1136,"parentId":1099,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\vendored\\contexts\\head-manager-context.js","layer":null},"startTime":1750489204673,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40205,"timestamp":287150196620,"id":1137,"parentId":1102,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\vendored\\contexts\\head-manager-context.js","layer":"pages-dir-node"},"startTime":1750489204673,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":40529,"timestamp":287150211647,"id":1140,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\vendored\\contexts\\html-context.js","layer":"pages-dir-node"},"startTime":1750489204688,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":41797,"timestamp":287150211993,"id":1141,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\route-modules\\pages\\vendored\\contexts\\html-context.js","layer":null},"startTime":1750489204688,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":22430,"timestamp":287150246175,"id":1147,"parentId":1125,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\denormalize-page-path.js","layer":null},"startTime":1750489204723,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26939,"timestamp":287150245442,"id":1146,"parentId":1124,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\denormalize-page-path.js","layer":"pages-dir-node"},"startTime":1750489204722,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":50171,"timestamp":287150230930,"id":1144,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\lib\\trace\\tracer.js","layer":"pages-dir-node"},"startTime":1750489204707,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":59962,"timestamp":287150231074,"id":1145,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\lib\\trace\\tracer.js","layer":null},"startTime":1750489204707,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":62356,"timestamp":287150230518,"id":1142,"parentId":1107,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\lib\\trace\\utils.js","layer":"pages-dir-node"},"startTime":1750489204707,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":66151,"timestamp":287150230807,"id":1143,"parentId":1108,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\lib\\trace\\utils.js","layer":null},"startTime":1750489204707,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":16199,"timestamp":287150298353,"id":1149,"parentId":1125,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\normalize-page-path.js","layer":null},"startTime":1750489204775,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":18518,"timestamp":287150297911,"id":1148,"parentId":1124,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\normalize-page-path.js","layer":"pages-dir-node"},"startTime":1750489204774,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":13203,"timestamp":287150322934,"id":1152,"parentId":1147,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\index.js","layer":null},"startTime":1750489204799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15366,"timestamp":287150322276,"id":1150,"parentId":1147,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\normalize-path-sep.js","layer":null},"startTime":1750489204799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":16214,"timestamp":287150322666,"id":1151,"parentId":1146,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\normalize-path-sep.js","layer":"pages-dir-node"},"startTime":1750489204799,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":16983,"timestamp":287150323144,"id":1153,"parentId":1146,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\index.js","layer":"pages-dir-node"},"startTime":1750489204800,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15544,"timestamp":287150334013,"id":1155,"parentId":1148,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\ensure-leading-slash.js","layer":"pages-dir-node"},"startTime":1750489204810,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":16945,"timestamp":287150333703,"id":1154,"parentId":1149,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\page-path\\ensure-leading-slash.js","layer":null},"startTime":1750489204810,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":9919,"timestamp":287150342766,"id":1156,"parentId":1144,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-thenable.js","layer":"pages-dir-node"},"startTime":1750489204819,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":10220,"timestamp":287150343605,"id":1157,"parentId":1145,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\is-thenable.js","layer":null},"startTime":1750489204820,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":7654,"timestamp":287150359819,"id":1163,"parentId":1153,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-dynamic.js","layer":"pages-dir-node"},"startTime":1750489204836,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":29638,"timestamp":287150343953,"id":1158,"parentId":1144,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\lib\\trace\\constants.js","layer":"pages-dir-node"},"startTime":1750489204820,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":34582,"timestamp":287150344217,"id":1159,"parentId":1145,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\server\\lib\\trace\\constants.js","layer":null},"startTime":1750489204821,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":21026,"timestamp":287150359608,"id":1162,"parentId":1152,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\is-dynamic.js","layer":null},"startTime":1750489204836,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":26690,"timestamp":287150358357,"id":1160,"parentId":1152,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\sorted-routes.js","layer":null},"startTime":1750489204835,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":32032,"timestamp":287150359258,"id":1161,"parentId":1153,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\sorted-routes.js","layer":"pages-dir-node"},"startTime":1750489204836,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":14678,"timestamp":287150400403,"id":1164,"parentId":1163,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interception-routes.js","layer":"pages-dir-node"},"startTime":1750489204877,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":15778,"timestamp":287150402402,"id":1165,"parentId":1162,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\interception-routes.js","layer":null},"startTime":1750489204879,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":39797,"timestamp":287150419515,"id":1166,"parentId":1144,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@opentelemetry\\api\\index.js","layer":"pages-dir-node"},"startTime":1750489204896,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":64469,"timestamp":287150419870,"id":1167,"parentId":1145,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@opentelemetry\\api\\index.js","layer":null},"startTime":1750489204896,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":69260,"timestamp":287150422454,"id":1168,"parentId":1164,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\app-paths.js","layer":"pages-dir-node"},"startTime":1750489204899,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":71597,"timestamp":287150423006,"id":1169,"parentId":1165,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\router\\utils\\app-paths.js","layer":null},"startTime":1750489204899,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":8138,"timestamp":287150503427,"id":1170,"parentId":1168,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\segment.js","layer":"pages-dir-node"},"startTime":1750489204980,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":949732,"timestamp":287149562114,"id":1080,"parentId":1078,"tags":{"request":"next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!"},"startTime":1750489204039,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":949723,"timestamp":287149562136,"id":1081,"parentId":1078,"tags":{"request":"private-next-pages/_document"},"startTime":1750489204039,"traceId":"901c6e83d477b378"},{"name":"build-module-js","duration":9907,"timestamp":287150503788,"id":1171,"parentId":1169,"tags":{"name":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\shared\\lib\\segment.js","layer":null},"startTime":1750489204980,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":952124,"timestamp":287149562158,"id":1083,"parentId":1078,"tags":{"request":"next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!"},"startTime":1750489204039,"traceId":"901c6e83d477b378"},{"name":"make","duration":1101043,"timestamp":287149506097,"id":1078,"parentId":1077,"tags":{},"startTime":1750489203983,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":9745,"timestamp":287150620038,"id":1183,"parentId":1182,"tags":{},"startTime":1750489205096,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":40,"timestamp":287150629966,"id":1185,"parentId":1182,"tags":{},"startTime":1750489205106,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":14578,"timestamp":287150630387,"id":1186,"parentId":1182,"tags":{},"startTime":1750489205107,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":64,"timestamp":287150645109,"id":1187,"parentId":1182,"tags":{},"startTime":1750489205122,"traceId":"901c6e83d477b378"}]
[{"name":"optimize-chunk-modules","duration":114,"timestamp":287150646611,"id":1188,"parentId":1182,"tags":{},"startTime":1750489205123,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":20215,"timestamp":287150629913,"id":1184,"parentId":1182,"tags":{},"startTime":1750489205106,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":8105,"timestamp":287150655959,"id":1189,"parentId":1182,"tags":{},"startTime":1750489205132,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":18377,"timestamp":287150664172,"id":1190,"parentId":1182,"tags":{},"startTime":1750489205141,"traceId":"901c6e83d477b378"},{"name":"hash","duration":8017,"timestamp":287150687169,"id":1191,"parentId":1182,"tags":{},"startTime":1750489205164,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":507,"timestamp":287150695179,"id":1192,"parentId":1182,"tags":{},"startTime":1750489205172,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":432,"timestamp":287150695602,"id":1193,"parentId":1182,"tags":{},"startTime":1750489205172,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":55963,"timestamp":287150696065,"id":1194,"parentId":1182,"tags":{},"startTime":1750489205172,"traceId":"901c6e83d477b378"},{"name":"seal","duration":173573,"timestamp":287150616540,"id":1182,"parentId":1077,"tags":{},"startTime":1750489205093,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":1329524,"timestamp":287149503343,"id":1077,"parentId":797,"tags":{"name":"server"},"startTime":1750489203980,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-server","duration":5765452,"timestamp":287145077891,"id":797,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489199554,"traceId":"901c6e83d477b378"}]
[{"name":"ensure-page","duration":5816796,"timestamp":287145027221,"id":794,"parentId":3,"tags":{"inputPage":"/_error"},"startTime":1750489199504,"traceId":"901c6e83d477b378"},{"name":"handle-request","duration":33981276,"timestamp":287118433734,"id":116,"tags":{"url":"/dashboard"},"startTime":1750489172910,"traceId":"901c6e83d477b378"},{"name":"memory-usage","duration":18,"timestamp":287152415308,"id":1195,"parentId":116,"tags":{"url":"/dashboard","memory.rss":"540352512","memory.heapUsed":"298467456","memory.heapTotal":"339791872"},"startTime":1750489206892,"traceId":"901c6e83d477b378"},{"name":"client-error","duration":36,"timestamp":287153417363,"id":1196,"parentId":3,"tags":{"errorCount":3},"startTime":1750489207894,"traceId":"901c6e83d477b378"},{"name":"navigation-to-hydration","duration":42364000,"timestamp":287112654253,"id":1197,"parentId":3,"tags":{"pathname":"/dashboard","query":""},"startTime":1750489209497,"traceId":"901c6e83d477b378"},{"name":"client-error","duration":12,"timestamp":287161100346,"id":1211,"parentId":3,"tags":{"errorCount":3},"startTime":1750489215577,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":372765,"timestamp":287160893709,"id":1209,"parentId":1203,"tags":{"request":"next-app-loader?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489215370,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":452574,"timestamp":287160893665,"id":1206,"parentId":1203,"tags":{"request":"private-next-pages/_document"},"startTime":1750489215370,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":592060,"timestamp":287160889602,"id":1204,"parentId":1203,"tags":{"request":"private-next-pages/_app"},"startTime":1750489215369,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":640932,"timestamp":287160893623,"id":1205,"parentId":1203,"tags":{"request":"next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!"},"startTime":1750489215370,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":706197,"timestamp":287160893677,"id":1207,"parentId":1203,"tags":{"request":"next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!"},"startTime":1750489215370,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":720310,"timestamp":287160893699,"id":1208,"parentId":1203,"tags":{"request":"next-app-loader?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489215370,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":738549,"timestamp":287160893718,"id":1210,"parentId":1203,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750489215370,"traceId":"901c6e83d477b378"},{"name":"make","duration":3612451,"timestamp":287160877999,"id":1203,"parentId":1202,"tags":{},"startTime":1750489215354,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":40594,"timestamp":287164602040,"id":1230,"parentId":1229,"tags":{},"startTime":1750489219078,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":318,"timestamp":287164644534,"id":1232,"parentId":1229,"tags":{},"startTime":1750489219121,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":22439,"timestamp":287164645208,"id":1233,"parentId":1229,"tags":{},"startTime":1750489219122,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":45,"timestamp":287164667747,"id":1234,"parentId":1229,"tags":{},"startTime":1750489219144,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":14,"timestamp":287164667837,"id":1235,"parentId":1229,"tags":{},"startTime":1750489219144,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":26600,"timestamp":287164643908,"id":1231,"parentId":1229,"tags":{},"startTime":1750489219120,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":25107,"timestamp":287164679284,"id":1236,"parentId":1229,"tags":{},"startTime":1750489219156,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":13641,"timestamp":287164704721,"id":1237,"parentId":1229,"tags":{},"startTime":1750489219181,"traceId":"901c6e83d477b378"},{"name":"hash","duration":16108,"timestamp":287164729615,"id":1238,"parentId":1229,"tags":{},"startTime":1750489219206,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":417,"timestamp":287164745716,"id":1239,"parentId":1229,"tags":{},"startTime":1750489219222,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":561,"timestamp":287164746047,"id":1240,"parentId":1229,"tags":{},"startTime":1750489219222,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":7601,"timestamp":287164746661,"id":1241,"parentId":1229,"tags":{},"startTime":1750489219223,"traceId":"901c6e83d477b378"},{"name":"seal","duration":245095,"timestamp":287164588327,"id":1229,"parentId":1202,"tags":{},"startTime":1750489219065,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":4070583,"timestamp":287160877071,"id":1202,"parentId":1200,"tags":{"name":"server"},"startTime":1750489215354,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-server","duration":4091520,"timestamp":287160869617,"id":1200,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489215346,"traceId":"901c6e83d477b378"},{"name":"client-error","duration":73,"timestamp":287165033482,"id":1244,"parentId":3,"tags":{"errorCount":3},"startTime":1750489219510,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":181088,"timestamp":287165039556,"id":1245,"parentId":1243,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489219516,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":178519,"timestamp":287165042314,"id":1247,"parentId":1243,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":314142,"timestamp":287165042738,"id":1256,"parentId":1243,"tags":{"request":"next-client-pages-loader?absolutePagePath=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":423691,"timestamp":287165042374,"id":1249,"parentId":1243,"tags":{"request":"next-client-pages-loader?absolutePagePath=private-next-pages%2F_app&page=%2F_app!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":609704,"timestamp":287165042667,"id":1251,"parentId":1243,"tags":{"request":"next-client-pages-loader?absolutePagePath=private-next-pages%2F_error&page=%2F_error!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":609667,"timestamp":287165042747,"id":1257,"parentId":1243,"tags":{"request":"next-client-pages-loader?absolutePagePath=C%3A%5CUsers%5CWindows%2011%5CDocuments%5Caugment-projects%5CInnovative%20Centre%5Ccrm-student-service%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":625647,"timestamp":287165042713,"id":1254,"parentId":1243,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":625934,"timestamp":287165042602,"id":1250,"parentId":1243,"tags":{"request":"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\node_modules\\next\\dist\\client\\router.js"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":625888,"timestamp":287165042726,"id":1255,"parentId":1243,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":653041,"timestamp":287165042683,"id":1252,"parentId":1243,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":653805,"timestamp":287165042701,"id":1253,"parentId":1243,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDocuments%5C%5Caugment-projects%5C%5CInnovative%20Centre%5C%5Ccrm-student-service%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":659053,"timestamp":287165042358,"id":1248,"parentId":1243,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1750489219519,"traceId":"901c6e83d477b378"},{"name":"add-entry","duration":665987,"timestamp":287165041300,"id":1246,"parentId":1243,"tags":{"request":"./node_modules/next/dist/client/next-dev.js"},"startTime":1750489219518,"traceId":"901c6e83d477b378"},{"name":"make","duration":724178,"timestamp":287164983653,"id":1243,"parentId":1242,"tags":{},"startTime":1750489219460,"traceId":"901c6e83d477b378"},{"name":"chunk-graph","duration":27709,"timestamp":287165725814,"id":1259,"parentId":1258,"tags":{},"startTime":1750489220202,"traceId":"901c6e83d477b378"},{"name":"optimize-modules","duration":37,"timestamp":287165753701,"id":1261,"parentId":1258,"tags":{},"startTime":1750489220230,"traceId":"901c6e83d477b378"},{"name":"optimize-chunks","duration":481,"timestamp":287165753787,"id":1262,"parentId":1258,"tags":{},"startTime":1750489220230,"traceId":"901c6e83d477b378"},{"name":"optimize-tree","duration":210,"timestamp":287165754374,"id":1263,"parentId":1258,"tags":{},"startTime":1750489220231,"traceId":"901c6e83d477b378"},{"name":"optimize-chunk-modules","duration":23,"timestamp":287165754696,"id":1264,"parentId":1258,"tags":{},"startTime":1750489220231,"traceId":"901c6e83d477b378"},{"name":"optimize","duration":7776,"timestamp":287165753654,"id":1260,"parentId":1258,"tags":{},"startTime":1750489220230,"traceId":"901c6e83d477b378"},{"name":"module-hash","duration":5188,"timestamp":287165773078,"id":1265,"parentId":1258,"tags":{},"startTime":1750489220250,"traceId":"901c6e83d477b378"},{"name":"code-generation","duration":39973,"timestamp":287165778397,"id":1266,"parentId":1258,"tags":{},"startTime":1750489220255,"traceId":"901c6e83d477b378"},{"name":"hash","duration":21799,"timestamp":287165834870,"id":1267,"parentId":1258,"tags":{},"startTime":1750489220311,"traceId":"901c6e83d477b378"},{"name":"code-generation-jobs","duration":2353,"timestamp":287165856647,"id":1268,"parentId":1258,"tags":{},"startTime":1750489220333,"traceId":"901c6e83d477b378"},{"name":"module-assets","duration":1604,"timestamp":287165858465,"id":1269,"parentId":1258,"tags":{},"startTime":1750489220335,"traceId":"901c6e83d477b378"},{"name":"create-chunk-assets","duration":16590,"timestamp":287165860174,"id":1270,"parentId":1258,"tags":{},"startTime":1750489220337,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-generateClientManifest","duration":370,"timestamp":287165879779,"id":1272,"parentId":1242,"tags":{},"startTime":1750489220356,"traceId":"901c6e83d477b378"},{"name":"NextJsBuildManifest-createassets","duration":1144,"timestamp":287165879043,"id":1271,"parentId":1242,"tags":{},"startTime":1750489220355,"traceId":"901c6e83d477b378"},{"name":"seal","duration":182690,"timestamp":287165719552,"id":1258,"parentId":1242,"tags":{},"startTime":1750489220196,"traceId":"901c6e83d477b378"},{"name":"webpack-compilation","duration":922948,"timestamp":287164979475,"id":1242,"parentId":1228,"tags":{"name":"client"},"startTime":1750489219456,"traceId":"901c6e83d477b378"},{"name":"webpack-invalidated-client","duration":4249334,"timestamp":287161660630,"id":1228,"parentId":3,"tags":{"trigger":"manual"},"startTime":1750489216137,"traceId":"901c6e83d477b378"}]
