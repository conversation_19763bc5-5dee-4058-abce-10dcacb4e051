// Payment Verification API - Admin Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { PaymentStatus, TransactionType } from "@prisma/client";
import { z } from "zod";

const verifyPaymentSchema = z.object({
  verified: z.boolean(),
  notes: z.string().optional(),
});

// POST /api/payments/[id]/verify - Verify or dispute payment
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to verify payments
    if (!["admin", "accounting"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { verified, notes } = verifyPaymentSchema.parse(body);

    // Get existing payment
    const existingPayment = await prisma.paymentRecord.findUnique({
      where: { id: params.id },
    });

    if (!existingPayment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Update payment verification status
    const updatedPayment = await prisma.paymentRecord.update({
      where: { id: params.id },
      data: {
        status: verified ? PaymentStatus.verified : PaymentStatus.disputed,
        verifiedBy: session.user.id,
        verificationDate: new Date(),
        notes: notes || existingPayment.notes,
      },
      include: {
        recordedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        verifiedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Create financial transaction record
    await prisma.financialTransaction.create({
      data: {
        paymentRecordId: updatedPayment.id,
        transactionType: TransactionType.adjustment,
        amount: existingPayment.amount,
        description: `Payment ${verified ? "verified" : "disputed"}: ${notes || "No notes"}`,
        performedBy: session.user.id,
        ipAddress: "127.0.0.1", // TODO: Get real IP
      },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: verified ? "VERIFY_PAYMENT" : "DISPUTE_PAYMENT",
        resourceType: "PAYMENT_RECORD",
        resourceId: updatedPayment.id,
        oldValues: { status: existingPayment.status },
        newValues: { 
          status: verified ? PaymentStatus.verified : PaymentStatus.disputed,
          notes,
          verifiedBy: session.user.id,
          verificationDate: new Date(),
        },
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedPayment,
      message: `Payment ${verified ? "verified" : "disputed"} successfully`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
