// Resource Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { ResourceType, ResourceStatus } from "@prisma/client";
import { z } from "zod";

const createResourceSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  resourceType: z.nativeEnum(ResourceType),
  fileUrl: z.string().url("Invalid file URL"),
  fileName: z.string().min(1, "File name is required"),
  fileSize: z.number().positive().optional(),
  groupId: z.string().min(1, "Group ID is required"),
  isPublic: z.boolean().default(false),
});

const updateResourceSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  resourceType: z.nativeEnum(ResourceType).optional(),
  fileUrl: z.string().url().optional(),
  fileName: z.string().min(1).optional(),
  fileSize: z.number().positive().optional(),
  isPublic: z.boolean().optional(),
  status: z.nativeEnum(ResourceStatus).optional(),
});

// GET /api/resources - List resources
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const groupId = searchParams.get("groupId");
    const resourceType = searchParams.get("resourceType") as ResourceType | null;
    const status = searchParams.get("status") as ResourceStatus | null;

    const skip = (page - 1) * limit;

    const where: any = {};
    if (groupId) where.groupId = groupId;
    if (resourceType) where.resourceType = resourceType;
    if (status) where.status = status;

    // If user is a teacher, only show resources for their groups or public resources
    if (session.user.role === "teacher" && session.user.teacherId) {
      const teacherGroups = await prisma.group.findMany({
        where: { teacherId: session.user.teacherId },
        select: { id: true },
      });
      const groupIds = teacherGroups.map(g => g.id);
      
      where.OR = [
        { groupId: { in: groupIds } },
        { isPublic: true },
      ];
    }

    const [resources, total] = await Promise.all([
      prisma.resource.findMany({
        where,
        skip,
        take: limit,
        include: {
          group: {
            include: {
              course: {
                select: {
                  courseCode: true,
                  title: true,
                },
              },
              teacher: {
                include: {
                  staffUser: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          uploadedByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              accessLogs: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.resource.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        resources,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching resources:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/resources - Upload new resource
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to upload resources
    if (!["teacher", "manager"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createResourceSchema.parse(body);

    // Verify that the group exists and user has access
    const group = await prisma.group.findUnique({
      where: { id: validatedData.groupId },
      include: {
        teacher: {
          include: {
            staffUser: true,
          },
        },
      },
    });

    if (!group) {
      return NextResponse.json({ error: "Group not found" }, { status: 404 });
    }

    // If user is a teacher, verify they own this group
    if (session.user.role === "teacher" && group.teacher.staffUser.id !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const resource = await prisma.resource.create({
      data: {
        ...validatedData,
        uploadedBy: session.user.id,
      },
      include: {
        group: {
          include: {
            course: {
              select: {
                courseCode: true,
                title: true,
              },
            },
            teacher: {
              include: {
                staffUser: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        uploadedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: resource,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error uploading resource:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
