// Financial Reports API - Admin Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { PaymentStatus } from "@prisma/client";
import { z } from "zod";

const generateReportSchema = z.object({
  reportType: z.enum(["revenue", "payments", "transactions"]),
  periodStart: z.string().transform((str) => new Date(str)),
  periodEnd: z.string().transform((str) => new Date(str)),
});

// GET /api/reports - List generated reports
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const reportType = searchParams.get("reportType");

    const skip = (page - 1) * limit;

    const where: any = {};
    if (reportType) where.reportType = reportType;

    const [reports, total] = await Promise.all([
      prisma.financialReport.findMany({
        where,
        skip,
        take: limit,
        include: {
          generatedByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.financialReport.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        reports,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching reports:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/reports - Generate new financial report
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to generate reports
    if (!["admin", "accounting"].includes(session.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { reportType, periodStart, periodEnd } = generateReportSchema.parse(body);

    let reportData: any = {};

    switch (reportType) {
      case "revenue":
        reportData = await generateRevenueReport(periodStart, periodEnd);
        break;
      case "payments":
        reportData = await generatePaymentsReport(periodStart, periodEnd);
        break;
      case "transactions":
        reportData = await generateTransactionsReport(periodStart, periodEnd);
        break;
    }

    // Save the report
    const report = await prisma.financialReport.create({
      data: {
        reportType,
        periodStart,
        periodEnd,
        data: reportData,
        generatedBy: session.user.id,
      },
      include: {
        generatedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "GENERATE_REPORT",
        resourceType: "FINANCIAL_REPORT",
        resourceId: report.id,
        newValues: { reportType, periodStart, periodEnd },
        ipAddress: "127.0.0.1", // TODO: Get real IP
        userAgent: request.headers.get("user-agent") || "Unknown",
      },
    });

    return NextResponse.json({
      success: true,
      data: report,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error generating report:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper functions for generating different types of reports

async function generateRevenueReport(periodStart: Date, periodEnd: Date) {
  const payments = await prisma.paymentRecord.findMany({
    where: {
      paymentDate: {
        gte: periodStart,
        lte: periodEnd,
      },
      status: PaymentStatus.verified,
    },
  });

  const totalRevenue = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
  const paymentCount = payments.length;

  const revenueByMethod = payments.reduce((acc, payment) => {
    acc[payment.paymentMethod] = (acc[payment.paymentMethod] || 0) + Number(payment.amount);
    return acc;
  }, {} as Record<string, number>);

  return {
    summary: {
      totalRevenue,
      paymentCount,
      averagePayment: paymentCount > 0 ? totalRevenue / paymentCount : 0,
    },
    revenueByMethod,
    periodStart,
    periodEnd,
  };
}

async function generatePaymentsReport(periodStart: Date, periodEnd: Date) {
  const payments = await prisma.paymentRecord.findMany({
    where: {
      paymentDate: {
        gte: periodStart,
        lte: periodEnd,
      },
    },
    include: {
      recordedByUser: {
        select: {
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  const paymentsByStatus = payments.reduce((acc, payment) => {
    acc[payment.status] = (acc[payment.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const paymentsByMethod = payments.reduce((acc, payment) => {
    acc[payment.paymentMethod] = (acc[payment.paymentMethod] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    summary: {
      totalPayments: payments.length,
      paymentsByStatus,
      paymentsByMethod,
    },
    payments: payments.map(p => ({
      id: p.id,
      amount: p.amount,
      paymentDate: p.paymentDate,
      paymentMethod: p.paymentMethod,
      status: p.status,
      recordedBy: `${p.recordedByUser.firstName} ${p.recordedByUser.lastName}`,
    })),
    periodStart,
    periodEnd,
  };
}

async function generateTransactionsReport(periodStart: Date, periodEnd: Date) {
  const transactions = await prisma.financialTransaction.findMany({
    where: {
      timestamp: {
        gte: periodStart,
        lte: periodEnd,
      },
    },
    include: {
      performedByUser: {
        select: {
          firstName: true,
          lastName: true,
        },
      },
    },
  });

  const transactionsByType = transactions.reduce((acc, transaction) => {
    acc[transaction.transactionType] = (acc[transaction.transactionType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    summary: {
      totalTransactions: transactions.length,
      transactionsByType,
    },
    transactions: transactions.map(t => ({
      id: t.id,
      transactionType: t.transactionType,
      amount: t.amount,
      description: t.description,
      timestamp: t.timestamp,
      performedBy: `${t.performedByUser.firstName} ${t.performedByUser.lastName}`,
    })),
    periodStart,
    periodEnd,
  };
}
