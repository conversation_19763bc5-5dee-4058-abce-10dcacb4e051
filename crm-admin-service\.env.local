# Admin Service Environment Variables

# Database Connection (Enhanced Security)
ADMIN_DATABASE_URL="postgresql://postgres:password@localhost:5432/crm_admin_db"

# Authentication (NextAuth.js v5)
NEXTAUTH_SECRET="your-nextauth-secret-for-admin-service-development"
NEXTAUTH_URL="http://localhost:3000"

# Service URLs for Inter-Service Communication
STAFF_SERVICE_URL="http://localhost:3001"
STUDENT_SERVICE_URL="http://localhost:3002"
SHARED_TYPES_URL="http://localhost:3003"

# Service Authentication
SERVICE_API_KEY="development-service-api-key"
SERVICE_NAME="crm-admin-service"

# Enhanced Security Settings
MFA_REQUIRED=false
SESSION_TIMEOUT=1800
MAX_LOGIN_ATTEMPTS=3
LOCKOUT_DURATION=900
