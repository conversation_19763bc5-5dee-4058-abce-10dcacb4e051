"use client";

import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Shield, DollarSign, Users, FileText, BarChart3, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Please sign in to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">Enhanced security portal for financial data management</p>
          <p className="text-sm text-gray-500 mt-1">
            Welcome back, {session.user?.name} ({session.user?.role})
          </p>
        </div>
        <Button onClick={handleSignOut} variant="outline" className="flex items-center gap-2">
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
        <div className="flex items-center">
          <Shield className="h-5 w-5 text-blue-600 mr-2" />
          <span className="text-blue-800 font-medium">Enhanced Security Mode Active</span>
        </div>
        <p className="text-blue-700 text-sm mt-1">
          All actions are logged and monitored. MFA is required for sensitive operations.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">$0</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Payment Records</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Reports Generated</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Payment Management */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Management</CardTitle>
            <CardDescription>Record and track payment information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/payments")}
              >
                <div className="text-left">
                  <div className="font-medium">Record New Payment</div>
                  <div className="text-sm text-gray-600">Add payment records for students</div>
                </div>
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push("/dashboard/payments")}
              >
                <div className="text-left">
                  <div className="font-medium">Verify Payments</div>
                  <div className="text-sm text-gray-600">Review and verify payment records</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Fee Structures</div>
                  <div className="text-sm text-gray-600">Manage course fees and pricing</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* User Management */}
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
            <CardDescription>Manage users across all services</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Staff Users</div>
                  <div className="text-sm text-gray-600">Manage staff service users</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Student Users</div>
                  <div className="text-sm text-gray-600">Manage student service users</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Admin Users</div>
                  <div className="text-sm text-gray-600">Manage admin service users</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Financial Reports */}
        <Card>
          <CardHeader>
            <CardTitle>Financial Reports</CardTitle>
            <CardDescription>Generate and view financial reports</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Revenue Report</div>
                  <div className="text-sm text-gray-600">Monthly and yearly revenue analysis</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Payment Summary</div>
                  <div className="text-sm text-gray-600">Payment status and trends</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Audit Trail</div>
                  <div className="text-sm text-gray-600">View system audit logs</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* System Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>System Configuration</CardTitle>
            <CardDescription>Configure system settings and security</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Security Settings</div>
                  <div className="text-sm text-gray-600">MFA, session timeout, access control</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">System Configuration</div>
                  <div className="text-sm text-gray-600">Business rules and system settings</div>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <div className="text-left">
                  <div className="font-medium">Backup & Recovery</div>
                  <div className="text-sm text-gray-600">Data backup and recovery options</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
