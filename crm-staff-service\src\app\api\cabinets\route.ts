// Cabinets (Classrooms) Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { CabinetStatus } from "@prisma/client";
import { z } from "zod";

const createCabinetSchema = z.object({
  cabinetNumber: z.string().min(1, "Cabinet number is required"),
  cabinetName: z.string().optional(),
  capacity: z.number().positive("Capacity must be positive"),
  equipment: z.array(z.string()).optional(),
  location: z.string().optional(),
});

const updateCabinetSchema = z.object({
  cabinetNumber: z.string().min(1).optional(),
  cabinetName: z.string().optional(),
  capacity: z.number().positive().optional(),
  equipment: z.array(z.string()).optional(),
  location: z.string().optional(),
  status: z.nativeEnum(CabinetStatus).optional(),
});

// GET /api/cabinets - List cabinets
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") as CabinetStatus | null;
    const location = searchParams.get("location");

    const skip = (page - 1) * limit;

    const where: any = {};
    if (status) where.status = status;
    if (location) where.location = { contains: location, mode: "insensitive" };

    const [cabinets, total] = await Promise.all([
      prisma.cabinet.findMany({
        where,
        skip,
        take: limit,
        include: {
          groups: {
            select: {
              id: true,
              groupName: true,
              currentStudents: true,
              maxStudents: true,
              status: true,
              startDate: true,
              endDate: true,
              schedule: true,
              course: {
                select: {
                  courseCode: true,
                  title: true,
                },
              },
              teacher: {
                include: {
                  staffUser: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
            where: {
              status: "active",
            },
          },
          _count: {
            select: {
              groups: true,
            },
          },
        },
        orderBy: { cabinetNumber: "asc" },
      }),
      prisma.cabinet.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        cabinets,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching cabinets:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/cabinets - Create new cabinet
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create cabinets
    if (session.user.role !== "manager") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createCabinetSchema.parse(body);

    // Check if cabinet number already exists
    const existingCabinet = await prisma.cabinet.findUnique({
      where: { cabinetNumber: validatedData.cabinetNumber },
    });

    if (existingCabinet) {
      return NextResponse.json(
        { error: "Cabinet number already exists" },
        { status: 400 }
      );
    }

    const cabinet = await prisma.cabinet.create({
      data: {
        ...validatedData,
        equipment: validatedData.equipment || [],
      },
    });

    return NextResponse.json({
      success: true,
      data: cabinet,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating cabinet:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
