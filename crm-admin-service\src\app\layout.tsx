import type { Metada<PERSON> } from "next";
import { SessionProvider } from "next-auth/react";
import "../styles/globals.css";

export const metadata: Metadata = {
  title: "Admin Portal - Innovative Centre CRM",
  description: "Enhanced security admin portal for financial data management",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gray-50">
        <SessionProvider>
          <div className="flex min-h-screen">
            {/* Sidebar will be added here */}
            <main className="flex-1">
              {children}
            </main>
          </div>
        </SessionProvider>
      </body>
    </html>
  );
}
