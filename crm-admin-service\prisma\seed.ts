import { PrismaClient, AdminRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding admin database...');

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);
  
  const adminUser = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: AdminRole.admin,
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+1234567890',
      mfaEnabled: false, // Disabled for development
      isActive: true,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create default cashier user
  const cashierUser = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: AdminRole.cashier,
      firstName: 'John',
      lastName: 'Cashier',
      phone: '+**********',
      mfaEnabled: false,
      isActive: true,
    },
  });

  console.log('✅ Created cashier user:', cashierUser.email);

  // Create default accounting user
  const accountingUser = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash: hashedPassword,
      role: AdminRole.accounting,
      firstName: 'Jane',
      lastName: 'Accountant',
      phone: '+**********',
      mfaEnabled: false,
      isActive: true,
    },
  });

  console.log('✅ Created accounting user:', accountingUser.email);

  // Create some sample system configuration
  await prisma.systemConfig.upsert({
    where: { configKey: 'system.name' },
    update: {},
    create: {
      configKey: 'system.name',
      configValue: 'Innovative Centre CRM',
      description: 'System name displayed in the application',
      updatedBy: adminUser.id,
    },
  });

  await prisma.systemConfig.upsert({
    where: { configKey: 'security.session_timeout' },
    update: {},
    create: {
      configKey: 'security.session_timeout',
      configValue: 1800, // 30 minutes
      description: 'Session timeout in seconds',
      updatedBy: adminUser.id,
    },
  });

  await prisma.systemConfig.upsert({
    where: { configKey: 'security.mfa_required' },
    update: {},
    create: {
      configKey: 'security.mfa_required',
      configValue: false,
      description: 'Whether MFA is required for all users',
      updatedBy: adminUser.id,
    },
  });

  console.log('✅ Created system configuration');

  console.log('🎉 Seeding completed successfully!');
  console.log('');
  console.log('Default users created:');
  console.log('- Admin: <EMAIL> / admin123');
  console.log('- Cashier: <EMAIL> / admin123');
  console.log('- Accounting: <EMAIL> / admin123');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
