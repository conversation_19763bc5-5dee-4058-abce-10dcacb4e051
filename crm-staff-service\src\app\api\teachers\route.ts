// Teachers Management API - Staff Service

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { TeacherStatus, StaffRole } from "@prisma/client";
import { z } from "zod";
import bcrypt from "bcryptjs";

const createTeacherSchema = z.object({
  // Staff user information
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  phone: z.string().optional(),
  employeeId: z.string().min(1, "Employee ID is required"),
  department: z.string().optional(),
  hireDate: z.string().transform((str) => new Date(str)),
  
  // Teacher-specific information
  specialization: z.string().optional(),
  qualifications: z.array(z.object({
    degree: z.string(),
    institution: z.string(),
    year: z.number(),
  })).optional(),
});

const updateTeacherSchema = z.object({
  // Staff user information
  email: z.string().email().optional(),
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  phone: z.string().optional(),
  department: z.string().optional(),
  isActive: z.boolean().optional(),
  
  // Teacher-specific information
  specialization: z.string().optional(),
  qualifications: z.array(z.object({
    degree: z.string(),
    institution: z.string(),
    year: z.number(),
  })).optional(),
  status: z.nativeEnum(TeacherStatus).optional(),
});

// GET /api/teachers - List teachers
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status") as TeacherStatus | null;
    const specialization = searchParams.get("specialization");

    const skip = (page - 1) * limit;

    const where: any = {};
    if (status) where.status = status;
    if (specialization) where.specialization = { contains: specialization, mode: "insensitive" };

    const [teachers, total] = await Promise.all([
      prisma.teacher.findMany({
        where,
        skip,
        take: limit,
        include: {
          staffUser: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              phone: true,
              employeeId: true,
              department: true,
              hireDate: true,
              isActive: true,
            },
          },
          groups: {
            select: {
              id: true,
              groupName: true,
              currentStudents: true,
              maxStudents: true,
              status: true,
              course: {
                select: {
                  courseCode: true,
                  title: true,
                },
              },
            },
          },
          kpis: {
            take: 5,
            orderBy: { calculatedAt: "desc" },
          },
          _count: {
            select: {
              groups: true,
              kpis: true,
              performanceReviews: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      }),
      prisma.teacher.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        teachers,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching teachers:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/teachers - Create new teacher
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission to create teachers
    if (session.user.role !== "manager") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createTeacherSchema.parse(body);

    // Check if email already exists
    const existingUser = await prisma.staffUser.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email already exists" },
        { status: 400 }
      );
    }

    // Check if employee ID already exists
    const existingEmployee = await prisma.staffUser.findUnique({
      where: { employeeId: validatedData.employeeId },
    });

    if (existingEmployee) {
      return NextResponse.json(
        { error: "Employee ID already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const passwordHash = await bcrypt.hash(validatedData.password, 12);

    // Create staff user and teacher in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create staff user
      const staffUser = await tx.staffUser.create({
        data: {
          email: validatedData.email,
          passwordHash,
          role: StaffRole.teacher,
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          phone: validatedData.phone,
          employeeId: validatedData.employeeId,
          department: validatedData.department,
          hireDate: validatedData.hireDate,
        },
      });

      // Create teacher profile
      const teacher = await tx.teacher.create({
        data: {
          staffUserId: staffUser.id,
          specialization: validatedData.specialization,
          qualifications: validatedData.qualifications || [],
        },
        include: {
          staffUser: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              phone: true,
              employeeId: true,
              department: true,
              hireDate: true,
              isActive: true,
            },
          },
        },
      });

      return teacher;
    });

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating teacher:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
